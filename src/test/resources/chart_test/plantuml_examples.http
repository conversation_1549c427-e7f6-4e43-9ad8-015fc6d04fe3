### 创建简单的PlantUML序列图
POST http://127.0.0.1:8080/create_chart
Content-Type: application/json

{
  "name": "简单序列图",
  "library": "plantuml",
  "charts": "plantuml",
  "configs": {},
  "titles": [],
  "rawData": "@startuml\nAlice -> Bob: Hello\nBob -> Alice: Hi there\n@enduml"
}

### 创建PlantUML类图
POST http://127.0.0.1:8080/create_chart
Content-Type: application/json

{
  "name": "用户管理类图",
  "library": "plantuml",
  "charts": "plantuml",
  "configs": {},
  "titles": [],
  "rawData": "@startuml\nclass User {\n  +String name\n  +String email\n  +login()\n  +logout()\n}\n\nclass Admin {\n  +manageUsers()\n}\n\nUser <|-- Admin\n@enduml"
}

### 创建PlantUML用例图
POST http://127.0.0.1:8080/create_chart
Content-Type: application/json

{
  "name": "系统用例图",
  "library": "plantuml",
  "charts": "plantuml",
  "configs": {},
  "titles": [],
  "rawData": "@startuml\nleft to right direction\nactor User\nactor Admin\n\nrectangle System {\n  User --> (Login)\n  User --> (View Profile)\n  Admin --> (Manage Users)\n  Admin --> (View Reports)\n}\n@enduml"
}

### 创建PlantUML活动图
POST http://127.0.0.1:8080/create_chart
Content-Type: application/json

{
  "name": "登录流程活动图",
  "library": "plantuml",
  "charts": "plantuml",
  "configs": {},
  "titles": [],
  "rawData": "@startuml\nstart\n:输入用户名和密码;\nif (验证成功?) then (yes)\n  :登录成功;\n  :跳转到主页;\nelse (no)\n  :显示错误信息;\n  :返回登录页面;\nendif\nstop\n@enduml"
}

### 创建PlantUML组件图
POST http://127.0.0.1:8080/create_chart
Content-Type: application/json

{
  "name": "系统架构组件图",
  "library": "plantuml",
  "charts": "plantuml",
  "configs": {},
  "titles": [],
  "rawData": "@startuml\npackage \"Web Layer\" {\n  [Controller]\n}\n\npackage \"Service Layer\" {\n  [UserService]\n  [ChartService]\n}\n\npackage \"Data Layer\" {\n  [Database]\n  [Redis]\n}\n\n[Controller] --> [UserService]\n[Controller] --> [ChartService]\n[UserService] --> [Database]\n[ChartService] --> [Redis]\n@enduml"
}

### 测试错误处理 - 无效的PlantUML代码
POST http://127.0.0.1:8080/create_chart
Content-Type: application/json

{
  "name": "错误测试",
  "library": "plantuml",
  "charts": "plantuml",
  "configs": {},
  "titles": [],
  "rawData": "@startuml\n这是无效的PlantUML代码\n@enduml"
}
