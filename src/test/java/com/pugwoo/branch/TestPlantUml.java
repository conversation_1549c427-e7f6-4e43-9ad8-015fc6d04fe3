package com.pugwoo.branch;

import net.sourceforge.plantuml.FileFormat;
import net.sourceforge.plantuml.FileFormatOption;
import net.sourceforge.plantuml.SourceStringReader;
import org.junit.jupiter.api.Test;

import java.io.FileOutputStream;
import java.io.OutputStream;

public class TestPlantUml {

    @Test
    public void test() throws Exception{

        // PlantUML 描述
        String uml = "@startuml\nAlice -> Bob: Hello\n@enduml";

        // 创建 SourceStringReader 对象
        SourceStringReader reader = new SourceStringReader(uml);

        // 输出 SVG 文件
        try (OutputStream svg = new FileOutputStream("d:/plant-uml-output.svg")) {
            // generateImage 方法的第二个参数指定输出格式，可以使用 "svg"、"png" 等
            String desc = reader.generateImage(svg, new FileFormatOption(FileFormat.SVG));
            // 你也可以获取更多描述信息
            System.out.println(desc);
        }

        System.out.println("SVG 图片已生成：output.svg");

    }

}
