package com.pugwoo.branch;

import com.pugwoo.branch.git.service.ICheckScanningService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

@SpringBootTest
public class TestScanCode {

    @Autowired
    ICheckScanningService checkScanningService;

    @Test
    public void check() throws IOException {
        checkScanningService.checkScanning("C:\\tmp\\repos\\test1",15L);
    }

    @Test
    public void testSearch() throws Exception {
        String dir = "c:/tmp/repos/common-service";

        String searchReg = "PinyinUtils";

        Pattern pattern = Pattern.compile(searchReg, Pattern.CASE_INSENSITIVE); // 默认忽略大小写

        List<File> files = listFiles(new File(dir));
        for(File file : files) {
            BufferedReader br = new BufferedReader(new FileReader(file));
            String line = null;
            int lineCount = 1;
            while((line = br.readLine()) != null) {
                if(pattern.matcher(line).find()) {
                    System.out.println(file.getAbsolutePath() + "[" + lineCount + "]:" + line.trim());
                }
                lineCount++;
            }
        }
    }

    public static List<File> listFiles(File file) {
        // 忽略.git目录和.gitignore文件
        String fileName = file.getName();
        if(fileName.equals(".git") || fileName.equals(".gitignore")) {
            return new ArrayList<>();
        }
        // 忽略一些常见的二进制文件
        if(fileName.toLowerCase().endsWith(".jpg") || fileName.toLowerCase().endsWith(".png")) {
            return new ArrayList<>();
        }

        if (file.isFile()) {
            return ListUtils.newArrayList(file);
        }

        List<File> result = new ArrayList<>();
        File[] files = file.listFiles();
        if(files == null) {
            return result;
        }
        for (File f : files) {
            result.addAll(listFiles(f));
        }
        return result;
    }

}
