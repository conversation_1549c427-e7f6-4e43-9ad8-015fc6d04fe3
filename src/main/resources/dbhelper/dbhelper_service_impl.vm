
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.admin.bean.ResultBean;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ${table.className}ServiceImpl implements ${table.className}Service {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public ${table.className}DO getById(#foreach($col in $columns)#if($col.key)#if($foreach.count>1), #end$col.javaType ${col.javaVarName}#end#end) {
#foreach($col in $columns)
#if($col.key)        if($col.javaVarName == null) {
           return null;
        }
#end
#end
        return dbHelper.getByKey(${table.className}DO.class, #foreach($col in $columns)#if($col.key)#if($foreach.count>1), #end${col.javaVarName}#end#end);
    }

    @Override
    public PageData<${table.className}DO> getPage(int page, int pageSize) {
        return dbHelper.getPage(${table.className}DO.class, page, pageSize);
    }

    @Override
    public ResultBean<Long> insertOrUpdate(${table.className}DO ${table.lowClassName}DO) {
        if(${table.lowClassName}DO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(${table.lowClassName}DO);
        return rows > 0 ? ResultBean.ok(${table.lowClassName}DO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(#foreach($col in $columns)#if($col.key)#if($foreach.count>1), #end$col.javaType ${col.javaVarName}#end#end) {
#foreach($col in $columns)
#if($col.key)
        if($col.javaVarName == null) {
            return false;
        }
#end
#end

        ${table.className}DO ${table.lowClassName}DO = new ${table.className}DO();
#foreach($col in $columns)
#if($col.key)
        ${table.lowClassName}DO.set${col.upJavaVarName}($col.javaVarName);
#end
#end
        return dbHelper.delete(${table.lowClassName}DO) > 0;
    }

}