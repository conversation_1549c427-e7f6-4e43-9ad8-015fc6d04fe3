monitors:
  - name: 探活
    code: HEALTH
    monitorSql: select 1 as count
    intervalSecond: 10
    succLogRateSecs: 3600
    assertExpression: |
      rows[0].count == 1

  - name: 检查表锁等待
    code: WAITING_TABLE_LOCK
    monitorSql: |
      SELECT count(*) as cnt FROM sys.schema_table_lock_waits where waiting_query_secs>10
    intervalSecond: 15
    succLogRateSecs: 3600
    detailSql: |
      select * FROM sys.schema_table_lock_waits;
      SHOW ENGINE INNODB STATUS;
    assertExpression: |
      rows[0].cnt == 0

  - name: 检查InnoDB行锁等待
    code: WAITING_INNODB_ROW_LOCK
    monitorSql: |
      SELECT count(*) as cnt FROM sys.innodb_lock_waits where wait_age_secs>60
    intervalSecond: 15
    succLogRateSecs: 3600
    detailSql: |
      select * FROM sys.innodb_lock_waits;
      SELECT event_id, sql_text, CURRENT_SCHEMA 
      FROM performance_schema.events_statements_current 
      ORDER BY event_id DESC LIMIT 1;
      SHOW ENGINE INNODB STATUS;
    assertExpression: |
      rows[0].cnt == 0

  - name: 检查长事务
    code: LONG_TRANSACTION
    monitorSql: |
      SELECT count(*) as cnt FROM information_schema.innodb_trx WHERE TIME_TO_SEC(TIMEDIFF(NOW(),trx_started))>3600
    intervalSecond: 30
    succLogRateSecs: 3600
    detailSql: |
      select * FROM information_schema.innodb_trx WHERE TIME_TO_SEC(TIMEDIFF(NOW(),trx_started))>3600;
      SELECT p.*
      FROM information_schema.processlist p
      JOIN information_schema.innodb_trx t
      ON p.ID = t.trx_mysql_thread_id
      WHERE TIME_TO_SEC(TIMEDIFF(NOW(),t.trx_started))>600;
    assertExpression: |
      rows[0].cnt == 0

  - name: 脏页比例
    code: DIRTY_PAGE_RATIO
    monitorSql: |
      SHOW GLOBAL STATUS WHERE Variable_name = 'Innodb_buffer_pool_pages_dirty';
      SHOW GLOBAL STATUS WHERE Variable_name = 'Innodb_buffer_pool_pages_total';
    intervalSecond: 300
    succLogRateSecs: 86400
    detailSql:
      SHOW ENGINE INNODB STATUS;
    assertExpression: |
      rows[0].Value / rows2[0].Value < 0.75