spring:
  application:
    name: branch
  profiles:
    active: local

mysqlArgs: useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true

management:
  endpoints:
    web:
      exposure:
        include: health, info, beans, prometheus

admin:
  systemName: 分支管理系统
  enableWebLogToDB: true
  token:
    expireSecond: 2592000 # 30天
  isActuallySendMsg: true
  mail:
    sender: <EMAIL>
    password: iuafjhydvofliiaa
    smtphost: smtp.qq.com
