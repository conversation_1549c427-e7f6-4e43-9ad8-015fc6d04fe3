Highcharts.chart('container', {
    chart: {
        type: 'area'
    },
    title: {
        useHTML: true,
        text: '$TITLE'
    },
    subtitle: {
        text: ''
    },
    accessibility: {
        point: {
            valueDescriptionFormat: '{index}. {point.category}, {point.y:,' +
                '.1f}, {point.percentage:.1f}%.'
        }
    },
    yAxis: {
        labels: {
            format: '{value}%'
        },
        title: {
            enabled: false
        }
    },
    xAxis: {
        categories: $DATE_ARRAY
    },
    tooltip: {
        pointFormat: '<span style="color:{series.color}">{series.name}</span>' +
            ': <b>{point.percentage:.1f}%</b> ({point.y:,.1f})<br/>',
        split: true
    },
    plotOptions: {
        series: {
            label: {
                style: {
                    fontSize: '1.4em',
                    opacity: 0.4
                }
            }
        },
        area: {
            stacking: 'percent',
            marker: {
                enabled: false
            }
        }
    },
    series: [
        #foreach ($item in $SERIES)
        {
            name: '$item.name',
            data: $item.data
        },
        #end
    ]
});
