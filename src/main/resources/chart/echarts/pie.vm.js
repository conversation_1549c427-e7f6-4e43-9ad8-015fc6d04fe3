var dom = document.getElementById('chart-container');
var myChart = echarts.init(dom, null, {
    renderer: 'canvas',
    useDirtyRect: false
});
var app = {};

var option;

option = {
    title: {
        text: '$TITLE',
        subtext: '',
        left: 'center'
    },
    tooltip: {
        trigger: 'item'
    },
    legend: {
        show: $showLegend,
        orient: 'vertical',
        left: 'right'
    },
    label: {
        show: true,
        formatter: '$label'
    },
    series: [
        {
            name: '',
            type: 'pie',
            radius: '60%', // 控制饼的大小
            data: [
                #foreach ($item in $SERIES)
                { value: $item.value, name: '$item.name' },
                #end
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }
    ]
};

if (option && typeof option === 'object') {
    myChart.setOption(option);
}

window.addEventListener('resize', myChart.resize);