var dom = document.getElementById('chart-container');
var myChart = echarts.init(dom, null, {
    renderer: 'canvas',
    useDirtyRect: false
});
var app = {};

var option;

option = {
    title: {
        text: '$TITLE'
    },
    tooltip: {
        trigger: 'axis'
    },
    legend: {
        data: $DIM_ARRAY
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: #if($showZoom) '7%' #else '3%' #end,
        containLabel: true
    },
    #if($showZoom)
    dataZoom: [
        {
            type: 'slider',
            start: 0,   // 起始百分比
            end: 100,   // 结束百分比
            height: 20, // 滑动条的高度
        }
    ],
    #end
    toolbox: {
        feature: {
            saveAsImage: {}
        }
    },
    xAxis: {
        #if($isDatesAsNumber) type: 'value', #else type: 'category', #end
        name: '$xAxisName',
        boundaryGap: true, // 让图形不要贴着y轴太近
        splitLine: {
            show: false // 不显示竖线的分割线
        },
        #if(!$isDatesAsNumber)
        data: $DATE_ARRAY
        #end
    },
    #if($isMultiYAxis)
    yAxis: [
        #foreach ($o in $leftYAxis)
        {
            type: 'value',
            name: '$o',
            position: 'left',
        },
        #end
        #foreach ($o in $rightYAxis)
        {
            type: 'value',
            name: '$o.name',
            position: 'right',
            offset: $o.offset
        },
        #end
    ],
    #else
    yAxis: {
        type: 'value',
        name: '$yAxisName'
    },
    #end
    series: [
        #foreach ($item in $SERIES)
        {
            name: '$item.name',
            #if($item.style && $item.style == "bar")
            type: 'bar',
            #else
            type: 'line',
            #end

            #if($isMultiYAxis)
            yAxisIndex: $item.yAxisIndex ,
            #end

            #if($item.color)
            itemStyle: {color: '$item.color'},
            #end
            #if($item.style && $item.style != "bar")
            lineStyle: {type: '$item.style'},
            #end
            smooth: $item.smooth,
            connectNulls: $item.connectNulls,
            showSymbol: false, // 不要显示线上面的小圈圈
            data: $item.data
        },
        #end
    ]
};

if (option && typeof option === 'object') {
    myChart.setOption(option);
}

window.addEventListener('resize', myChart.resize);