spring:
  datasource:
    url: **********************************?${mysqlArgs}
    username: idc
    password: idc
    hikari:
      max-lifetime: 120000 # 由于代理的存在，数据库连接idle10分钟可能就失效了，远没有到mysql的8小时
  data:
    redis:
      host: *********
      port: 6379
      password: Redis118229SZ
      database: 10

admin:
  securityLevel: LOOSE
  notifyWhenRestart: true

branch:
  enableDroneRepoControl: false # 是否开启根据drone进行git权限判断
  droneBaseUrlList: # 如果使用drone进行编译则用这个，多个则逗号隔开
  editorHost: http://*********:31680
