#set($page_title='数据扫描配置')

#parse("database/data_scan/data_scan_result.vm")
#parse("database/data_scan/data_scan_config_detail.vm")

<style>
    .database-table-expand label {width: 120px;color: #99a9bf;}
    .database-table-expand .el-form-item {width: 100%;}
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item>
            <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
            <el-button @click="getData">刷新</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column type="expand">
            <template slot-scope="props">
                <el-form :data="props" class="database-table-expand">
                    <el-form-item label="邮件通知">{{props.row.sendEmail}}</el-form-item>
                </el-form>
            </template>
        </el-table-column>
        <el-table-column prop="id" label="id" width="50"></el-table-column>
        <el-table-column prop="name" label="扫描任务的名称"></el-table-column>
        <el-table-column prop="databaseDO.name" label="数据库实例"></el-table-column>
        <el-table-column prop="databaseName" label="数据库名称"></el-table-column>
        <el-table-column prop="tableName" label="表名" min-width="150"></el-table-column>
        <el-table-column prop="idColumn" label="主键字段"></el-table-column>
        <el-table-column prop="lastKey" label="最后一次扫描主键">
            <template slot-scope="props">
                {{props.row.lastKey ? props.row.lastKey : '无'}}
                <el-tooltip class="item" effect="dark" content="如果主键落后于数据库最新主键太多，程序会按扫描间隔每次扫描1000条，请耐心等待或缩短扫描间隔秒数或直接修改最后扫描主键值" placement="right" style="display: inline">
                    <i class="el-icon-question"></i>
                </el-tooltip>
            </template>
        </el-table-column>
        <el-table-column prop="scanIntervalSecond" label="扫描间隔秒数"></el-table-column>
        <el-table-column prop="lastScanTime" label="最后一次扫描时间" width="180"></el-table-column>
        <el-table-column label="启用禁用" width="65">
            <template slot-scope="props">
                <el-switch v-model="props.row.enabled" active-color="#13ce66" inactive-color="#BEBEBE"
                           @change="enableScan($event, props.row.id)">
                </el-switch>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
            <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                <el-button type="primary" size="small" @click="editConfigDetail(scope.row)">规则</el-button>
                <el-button size="small" @click="showScanResult(scope.row)">扫描结果</el-button>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog title="编辑扫描规则" :visible.sync="showEditDetailDialog" top="10px" width="1400" :close-on-click-modal="false" @close="getData"
            v-if="showEditDetailDialog"> <!-- 加v-if让对话框销毁，这样里面内容下次打开时可以刷新-->
        <database-scan-detail :database-scan-config-id="showEditDetailConfigId" :result-main-columns="showEditDetailResultMainColumns"></database-scan-detail>
    </el-dialog>

    <el-dialog title="扫描结果" :visible.sync="showScanResultDialog" top="10px" width="1400"
            v-if="showScanResultDialog"> <!-- 加v-if让对话框销毁，这样里面内容下次打开时可以刷新-->
        <database-scan-result :database-scan-config-id="showScanResultConfigId"></database-scan-result>
    </el-dialog>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" width="1200" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="160px" :rules="rules" ref="addEditForm">
            <el-form-item label="* 任务名称" prop="name">
                <el-input v-model="addEditForm.name" placeholder="扫描任务的名称"></el-input>
            </el-form-item>
            <el-form-item label="* 扫描间隔(秒)" prop="scanIntervalSecond">
                <el-input v-model="addEditForm.scanIntervalSecond" placeholder="扫描间隔秒数"></el-input>
            </el-form-item>
            <el-form-item label="* 数据库配置id" prop="databaseId">
                <el-select v-model="addEditForm.databaseId" placeholder="请选择">
                    <el-option v-for="item in databases" :key="item.databaseId" :label="item.name" :value="item.databaseId">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="* 数据库名" prop="databaseName">
                <el-input v-model="addEditForm.databaseName" placeholder="扫描的数据库名称"></el-input>
            </el-form-item>
            <el-form-item label="* 表名" prop="tableName">
                <el-input v-model="addEditForm.tableName" placeholder="扫描的表名"></el-input>
            </el-form-item>
            <el-form-item label="* 主键字段" prop="idColumn">
                <el-input v-model="addEditForm.idColumn" placeholder="表的主键字段，用于实现增量扫描，只支持单个主键"></el-input>
            </el-form-item>
            <el-form-item label="最后一次扫描的主键" prop="lastKey" v-if="addEditForm.id">
                <el-input v-model="addEditForm.lastKey" placeholder="最后一次扫描的主键"></el-input>
            </el-form-item>
            <el-form-item label="异常通知邮箱" prop="sendEmail">
                <el-input v-model="addEditForm.sendEmail" placeholder="异常通知邮箱，多个分号隔开"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            databases: [], // 数据库下拉选择
            rules: {},
            total: 0, tableData: [], tableLoading: false,
            showDialog: false, dialogTitle: '',
            showEditDetailDialog: false, showEditDetailConfigId: null, showEditDetailResultMainColumns: '',
            showScanResultDialog: false, showScanResultConfigId: null
        },
        created: function() {
            var that = this
            this.getData()
            Resource.get("${_contextPath_}/database/get_database_for_select", {}, function(resp){
                that.databases = resp.data
            })
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/database_scan_config/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/database_scan_config/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/database_scan_config/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            },
            enableScan: function($event, configId) {
                var that = this
                Resource.post("${_contextPath_}/database_scan_config/enable_disable_config", {
                    configId: configId, enabled: $event
                }, function(resp){
                    Message.success("修改成功")
                    that.getData()
                })
            },
            editConfigDetail: function(row) {
                this.showEditDetailConfigId = row.id
                this.showEditDetailResultMainColumns = row.resultMainColumns
                this.showEditDetailDialog = true
            },
            showScanResult: function(row) {
                this.showScanResultConfigId = row.id
                this.showScanResultDialog = true
            }
        }
    })
</script>