<script type="text/x-template" id="processList">
    <div>
        <p>
            <span>Sleep且无锁无undo连接数: {{sleepAndNoLockConnections}}</span>
            <span v-show="!isPerformanceSchemaOn" style="color: red">performance_schema未开启，锁信息等可能不完整</span>
            <span v-show="queryLockCostMs>1000" style="color: red">查询锁信息耗时过长({{queryLockCostMs}}毫秒)</span>
            <span v-show="isStopAutoRefreshByCostMs" style="color: red">自动刷新已停止</span>
            <span style="float: right">
                <el-button @click="switchLockInfo">{{isExpandLock ? '隐藏锁信息' : '显示锁信息'}}</el-button>
                <el-button @click="switchAutoKill">{{(autoKillRunning ? '停止' : '启动') + '自动kill锁表'}}</el-button>
                <span v-show="autoKillRunning">已kill SQL数: {{autoKillSqlCount}}</span>
                <el-button type="primary" @click="getData" v-show="!isAutoRefresh">刷新</el-button>
                <span>自动刷新:</span>
                <el-tooltip class="item" effect="dark" placement="top" content="当1次查询数据库锁超过1秒时，将自动停止自动刷新">
                    <el-switch v-model="isAutoRefresh"></el-switch>
                </el-tooltip>
                <span>查询行锁明细:</span>
                <el-tooltip class="item" effect="dark" placement="top" content="是否使用performance_schema.data_locks查询行锁，生产环境严禁开启!!! 由于MySQL的性能原因，开启将随时导致数据库雪崩。">
                    <el-switch v-model="isQueryRowLock" active-color="red"></el-switch>
                </el-tooltip>
            </span>
        </p>
        <el-table :data="tableData" border stripe v-loading.body="tableLoading" :default-expand-all="true">
            <el-table-column prop="id" label="pid" width="80"></el-table-column>
            <el-table-column label="用户名@客户端ip/数据库" width="230">
                <template slot-scope="scope">
                    <span style="font-size: smaller;">{{scope.row.user}}@{{scope.row.clientIp}}{{scope.row.db ? '/' + scope.row.db : ''}}</span>
                </template>
            </el-table-column>
            <el-table-column label="Cmd" width="75">
                <template slot-scope="scope">
                    <span style="font-size: smaller;">{{scope.row.command}}</span>
                </template>
            </el-table-column>
            <el-table-column prop="sql" label="SQL">
                <template slot-scope="scope">
                    <span style="font-size: smaller;">{{scope.row.sql}}
                        <el-button size="small" @click="expandSql(scope.row)" v-show="scope.row.isShortSql">看全部</el-button>
                    </span>
                </template>
            </el-table-column>
            <el-table-column prop="time" width="100">
                <template slot="header" slot-scope="scope">
                    <el-tooltip class="item" effect="dark" content="当前状态已持续的时长" placement="top">
                        <span>运行_秒</span>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column label="状态" width="130">
                <template slot-scope="scope">
                    <span style="font-size: smaller;">{{scope.row.state}}</span>
                </template>
            </el-table-column>
            <el-table-column type="expand" v-if="isExpandLock">
                <template slot-scope="props">
                    <el-table :data="props.row.locks" border stripe v-if="props.row.locks"
                              :header-cell-style="{ background: '#ffbb5f', color: '#000000', fontSize: '12px' }"
                              :cell-style="{ fontSize: '12px', background: '#fffdf5', color: '#000000' }">
                        <el-table-column prop="scopeType" label="锁大类" min-width="150"></el-table-column>
                        <el-table-column prop="lockType" label="锁类型" min-width="200">
                            <template slot-scope="scope">
                                <span>{{scope.row.lockType}}({{scope.row.isWriteLock ? '写' : '读'}})</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="lockDuration" label="锁持续范围" min-width="150"></el-table-column>
                        <el-table-column prop="lockStatus" label="锁状态" min-width="120"></el-table-column>
                        <el-table-column prop="objectType" label="锁范围" min-width="120"></el-table-column>
                        <el-table-column prop="database" label="数据库" min-width="100"></el-table-column>
                        <el-table-column prop="table" label="表" min-width="100"></el-table-column>
                        <el-table-column prop="column" label="列" min-width="100"></el-table-column>
                        <el-table-column prop="row" label="行" min-width="100"></el-table-column>
                        <el-table-column prop="waitingSeconds" label="等待秒数" min-width="100"></el-table-column>
                        <el-table-column prop="waitProcessId" label="等待pid" min-width="100"></el-table-column>
                    </el-table>
                </template>
            </el-table-column>
            <el-table-column width="120" label="表锁">
                <template slot-scope="scope">
                    <span>持有:{{scope.row.holdReadLocks}}读,{{scope.row.holdWriteLocks}}写<br/>
                        <span :style="{ color: (scope.row.waitReadLocks + scope.row.waitWriteLocks) > 0 ? 'red' : 'inherit' }">
                            等待:{{scope.row.waitReadLocks}}读,{{scope.row.waitWriteLocks}}写</span></span>
                </template>
            </el-table-column>
            <el-table-column width="130" label="行锁">
                <template slot-scope="scope">
                    <span>持有:{{scope.row.holdRowLocks}}<br/>
                        <span :style="{ color: (scope.row.waitRowLocks) > 0 ? 'red' : 'inherit' }">
                            等待:{{scope.row.waitRowLocks}}</span></span>
                </template>
            </el-table-column>
            <el-table-column width="85" label="undo">
                <template slot-scope="scope">
                    <span>{{scope.row.undoEntries}}</span>
                </template>
            </el-table-column>
            <el-table-column width="70" label="操作">
                <template slot-scope="scope">
                    <el-button size="small" @click="kill(scope.row.id)" type="plain">Kill</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</script>

<script>
Vue.component('process-list', {
    template: '#processList',
    data: function () {
        return {
            tableLoading: false,
            tableData: [],
            sleepAndNoLockConnections: 0,
            autoKillRunning: false,
            autoKillIntervalId: null,
            autoKillSqlCount: 0,
            isPerformanceSchemaOn: true,
            isExpandLock: false,
            isAutoRefresh: true,
            autoRefreshTimeoutId: null,
            isRefreshing: false, // 是否处于请求中，这个是为了避免重复提交
            queryLockCostMs: 0,
            isStopAutoRefreshByCostMs: false,
            isQueryRowLock: false,
            confirmQueryRowLock: false
        }
    },
    props: {
        databaseId: Number
    },
    created: function() {
        this.getData()
    },
    watch: {
        isAutoRefresh: function() {
            if (this.isAutoRefresh) {
                this.isStopAutoRefreshByCostMs = false
                this.getData()
            } else {
                if (this.autoRefreshTimeoutId) {
                    clearTimeout(this.autoRefreshTimeoutId)
                    this.autoRefreshTimeoutId = null
                }
            }
        },
        isQueryRowLock: function() {
            var that = this
            if (this.isQueryRowLock) {
                Message.confirm("确定要开启查询行锁明细吗？生产环境严禁开启!!! 由于MySQL的性能原因，开启将随时导致数据库雪崩。",
                    function() {that.confirmQueryRowLock = true},
                    function() {that.confirmQueryRowLock = false; that.isQueryRowLock = false; })
            }
        }
    },
    beforeDestroy: function() {
        if (this.autoRefreshTimeoutId) {
            clearTimeout(this.autoRefreshTimeoutId)
            this.autoRefreshTimeoutId = null
        }
        this.isAutoRefresh = false
    },
    methods: {
        getData: function() {
            var that = this
            if (this.isRefreshing) {
                return;
            }
            that.tableLoading = true
            this.isRefreshing = true
            Resource.get("${_contextPath_}/database_feature/query_process_list", {
                databaseId: that.databaseId,
                isQueryRowLock: that.isQueryRowLock && that.confirmQueryRowLock
            }, function(resp){
                that.tableData = resp.data.processList
                for (let i = 0; i < that.tableData.length; i++) {
                    if (that.tableData[i].sql && that.tableData[i].sql.length > 300) {
                        that.tableData[i].fullSql = that.tableData[i].sql
                        that.tableData[i].sql = that.tableData[i].sql.substring(0, 300) + "..."
                        that.tableData[i].isShortSql = true
                    } else {
                        that.tableData[i].isShortSql = false
                    }
                }
                that.tableLoading = false
                that.sleepAndNoLockConnections = resp.data.sleepAndNoLockConnections
                that.isPerformanceSchemaOn = resp.data.isPerformanceSchemaOn
                that.queryLockCostMs = resp.data.queryLockCostMs
                if (that.queryLockCostMs > 1000 && that.isAutoRefresh) {
                    that.isAutoRefresh = false
                    that.isStopAutoRefreshByCostMs = true
                }
                if (that.isAutoRefresh) {
                    that.autoRefreshTimeoutId = setTimeout(function(){that.getData()}, 1000)
                }
            }, null, function () { // finally
                that.isRefreshing = false
            })
        },
        kill: function(threadId) {
            var that = this
            Resource.post("${_contextPath_}/database_feature/kill_thread", {
                databaseId: that.databaseId,
                threadId: threadId
            })
        },
        switchAutoKill: function() {
            var that = this
            if (!this.autoKillRunning) {
                Message.confirm("确定开启自动kill超过3秒的锁表SQL吗?", function () {
                    that.autoKillIntervalId = setInterval(function() {
                        Resource.post("${_contextPath_}/database_feature/kill_thread_batch", {
                            databaseId: that.databaseId,
                            state: "Waiting for table metadata lock",
                            executeSecond: 3
                        }, function(resp) {
                            that.autoKillSqlCount += resp.data
                        })
                    }, 1000)
                    that.autoKillRunning = true
                })
            } else {
                clearInterval(that.autoKillIntervalId)
                that.autoKillRunning = false
                that.autoKillSqlCount = 0
            }
        },
        switchLockInfo: function() {
            this.isExpandLock = !this.isExpandLock
        },
        expandSql: function (row) {
            row.sql = row.fullSql
            row.isShortSql = false
        }
    }
})
</script>