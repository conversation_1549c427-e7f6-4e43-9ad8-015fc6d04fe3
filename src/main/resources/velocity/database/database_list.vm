#set($page_title='数据库管理')

#parse("database/feature/table_size_info.vm")
#parse("database/feature/process_list.vm")
#parse("database/feature/system_variables.vm")

<style>
    .database-list-table-expand label {width: 140px;color: #99a9bf;}
    .database-list-table-expand .el-form-item {width: 100%;}
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item>
            <el-button type="success" @click="handleAddOrEdit(true)">新增数据库</el-button>
            <el-button @click="handleShowPassword">{{showPassword ? '隐藏秘密' : '显示密码'}}</el-button>
            <el-button @click="getData">刷新</el-button>
            <el-select v-model="queryForm.env" placeholder="环境" @change="getData">
                <el-option v-for="env in databaseEnvsWithEmpty" :key="env.value" :label="env.label" :value="env.value"></el-option>
            </el-select>
            <el-select v-model="queryForm.type" placeholder="类型" @change="getData">
                <el-option v-for="env in databaseTypesWithEmpty" :key="env.value" :label="env.value" :value="env.value"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item style="float: right">
            <el-button @click="resetConnectionPool">重置连接池</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column type="expand">
            <template slot-scope="props">
                <el-form :data="props" class="database-list-table-expand">
                    <el-form-item label="内置告警通知邮箱">{{props.row.monitorSendMail}}</el-form-item>
                </el-form>
            </template>
        </el-table-column>
        <el-table-column label="id" width="50">
            <template slot-scope="props">
                {{props.row.id}}
            </template>
        </el-table-column>
        <el-table-column label="连接名称" min-width="100">
            <template slot-scope="props">
                {{props.row.name}}
            </template>
        </el-table-column>
        <el-table-column label="环境" width="60">
            <template slot-scope="props">
                {{props.row.envLabel}}
            </template>
        </el-table-column>
        <el-table-column label="类型" width="110">
            <template slot-scope="props">
                {{props.row.type}}
            </template>
        </el-table-column>
        <el-table-column label="ip端口" min-width="110">
            <template slot-scope="props">
                {{props.row.host}}:{{props.row.port}}
            </template>
        </el-table-column>
        <el-table-column label="用户名" min-width="60">
            <template slot-scope="props">
                {{props.row.username}}
            </template>
        </el-table-column>
        <el-table-column label="密码" min-width="60" v-if="showPassword">
            <template slot-scope="props">
                {{showPassword ? props.row.password : '******'}}
            </template>
        </el-table-column>
        <el-table-column label="状态" width="120">
            <template slot-scope="props">
                <span v-show="props.row.connectSuccess" style="color: green">{{props.row.statusMsg}}</span>
                <span v-show="!props.row.connectSuccess" style="color: red">({{props.row.statusCode}}){{props.row.statusMsg}}</span>
            </template>
        </el-table-column>
        <el-table-column label="内置监控" width="100">
            <template slot-scope="props">
                <el-switch v-model="props.row.enableMonitor" active-color="#13ce66" inactive-color="#BEBEBE"
                            @change="enableMonitor($event, props.row.id)"></el-switch>
                <el-tooltip class="item" effect="dark" placement="bottom" v-if="props.row.type=='MySQL'">
                    <div slot="content">
                        MySQL账号需具备以下权限：
                        1) 全局的PROCESS权限 2) performance_schema库的SELECT权限 3) sys库的EXECUTE/SELECT/SHOW VIEW权限
                    </div>
                    <i class="el-icon-question"></i>
                </el-tooltip>
            </template>
        </el-table-column>
        <el-table-column label="容量监控" width="100">
            <template slot-scope="props">
                <el-tooltip class="item" effect="dark" placement="bottom">
                    <div slot="content">
                        <span v-if="props.row.type!='MySQL'">目前仅MySQL支持容量监控</span>
                        <span v-if="props.row.type=='MySQL'">当使用量>=90%或预估剩余天数<=6天时告警，每天上午9点检查</span>
                    </div>
                    <el-switch v-model="props.row.enableCapacityMonitor" active-color="#13ce66" inactive-color="#BEBEBE"
                           @change="enableCapacityMonitor($event, props.row.id)" :disabled="props.row.type!='MySQL'"></el-switch>
                </el-tooltip>
            </template>
        </el-table-column>
        <el-table-column min-width="120" label="操作">
            <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                <el-button-group>
                    <el-button size="small" @click="doShowTableSizeInfo(scope.row)"
                               v-show="scope.row.type=='ClickHouse' || scope.row.type=='MySQL'">空间</el-button>
                    <el-button size="small" @click="doShowProcessList(scope.row)"
                               v-show="scope.row.type=='MySQL'">进程</el-button>
                    <el-button size="small" @click="doShowSystemVariables(scope.row)"
                               v-show="scope.row.type=='MySQL'">变量</el-button>
                </el-button-group>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right;margin-bottom: 100px;" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog :title="addEditDialogTitle" :visible.sync="addEditDialogShow" top="10px" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="160px" :rules="addEditRules" ref="addEditForm">
            <el-form-item label="* 连接名称" prop="name">
                <el-input v-model="addEditForm.name" placeholder="自己随便取一个"></el-input>
            </el-form-item>
            <el-form-item label="* 环境" prop="env">
                <el-select v-model="addEditForm.env" placeholder="环境">
                    <el-option v-for="env in databaseEnvs" :key="env.value" :label="env.label" :value="env.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="* 类型" prop="type">
                <el-select v-model="addEditForm.type" placeholder="类型">
                    <el-option v-for="env in databaseTypes" :key="env.value" :label="env.value" :value="env.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="* 主机" prop="host">
                <el-input v-model="addEditForm.host" placeholder="主机"></el-input>
            </el-form-item>
            <el-form-item label="* 端口" prop="port">
                <el-input v-model.number="addEditForm.port" placeholder="端口"></el-input>
            </el-form-item>
            <el-form-item label="* 用户名" prop="username">
                <el-input v-model="addEditForm.username"></el-input>
            </el-form-item>
            <el-form-item label="* 密码" prop="password">
                <el-input v-model="addEditForm.password" show-password placeholder="密码"></el-input>
            </el-form-item>
            <el-form-item label="容量上限(GB)" prop="capacityGb">
                <el-input v-model="addEditForm.capacityGb" placeholder="单位GB，如开启容量监控，请设置"></el-input>
            </el-form-item>
            <el-form-item label="内置告警通知邮箱" prop="monitorSendMail">
                <el-input v-model="addEditForm.monitorSendMail"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete" v-show="addEditForm.id">删除</el-button>
            <el-button @click="testConnect">测试连接</el-button>
            <el-button @click="addEditDialogShow = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="表空间大小(非实时)" :visible.sync="showTableSizeInfo" top="10px" width="1500" v-if="showTableSizeInfo">
        <table-size-info :database-id="currentDatabaseId"></table-size-info>
    </el-dialog>
    <el-dialog title="SQL进程" :visible.sync="showProcessList" top="10px" width="1500" v-if="showProcessList"> <!-- v-if让窗口关闭时自动销毁 -->
        <process-list :database-id="currentDatabaseId"></process-list>
    </el-dialog>
    <el-dialog title="系统变量" :visible.sync="showSystemVariables" top="10px" width="1500" v-if="showSystemVariables">
        <system-variables :database-id="currentDatabaseId"></system-variables>
    </el-dialog>
</div>

<script>
var defaultQueryForm = {page: 1, pageSize: 50}
var defaultAddForm = {}
var vm = new Vue({
    el: '#app',
    data: {
        queryForm: Utils.copy(defaultQueryForm),
        addEditDialogShow: false,
        addEditDialogTitle: '',
        addEditForm: Utils.copy(defaultAddForm),
        addEditRules: {
            name: Form.notBlankValidator('连接名称不能为空'),
            env: Form.notBlankValidator('环境不能为空'),
            type: Form.notBlankValidator('类型不能为空'),
            host: Form.notBlankValidator('主机不能为空'),
            username: Form.notBlankValidator('用户名不能为空'),
            port: {
                validator: function(rule, value, callback) {
                    if (!(value && (value + '').trim())) {
                        callback(new Error('端口不能为空'));
                    } else if (!Number.isInteger(value)) {
                        callback(new Error('端口必须为数字'));
                    } else if (value < 0 || value > 65535) {
                        callback(new Error('端口取值范围 0~65535'));
                    } else {
                        callback();
                    }
                },
                trigger: 'blur',
            },
        },
        databaseEnvs: [
#foreach( $env in $envs )
    { value: "$env.code", label: "$env.name" },
#end
        ],
        databaseTypes: [
#foreach( $databaseType in $databaseTypes )
    { value: "$databaseType" },
#end
        ],
        databaseEnvsWithEmpty: [{value: "", label: ""},
            #foreach( $env in $envs )
                { value: "$env.code", label: "$env.name" },
            #end
        ],
        databaseTypesWithEmpty: [{value: ""},
            #foreach( $databaseType in $databaseTypes )
                { value: "$databaseType" },
            #end
        ],
        total: 0,
        tableData: [],
        tableLoading: false,
        loading: true,
        showPassword: false,
        currentDatabaseId: null,
        showTableSizeInfo: false,
        showProcessList: false,
        showSystemVariables: false
    },
    created: function () {
        this.getData()
    },
    methods: {
        getData: function () { // 获取数据库分页信息
            var that = this
            that.tableLoading = true
            Resource.get("${_contextPath_}/database/page", this.queryForm, function (resp) {
                that.tableData = resp.data.data
                that.total = resp.data.total
                that.tableLoading = false

                // 异步查询数据库的状态
                for (let i = 0; i < that.tableData.length; i++) {
                    that.$set(that.tableData[i], 'connectSuccess', true)
                    Resource.get("${_contextPath_}/database/get_database_status", {databaseId: that.tableData[i].id}, function (resp) {
                        that.$set(that.tableData[i], 'connectSuccess', resp.data.connectSuccess)
                        that.$set(that.tableData[i], 'statusCode', resp.data.statusCode)
                        that.$set(that.tableData[i], 'statusMsg', resp.data.statusMsg)
                    }, function(resp) {
                        that.$set(that.tableData[i], 'connectSuccess', false)
                        that.$set(that.tableData[i], 'statusCode', -1)
                        that.$set(that.tableData[i], 'statusMsg', resp.msg)
                    })
                }
            })
        },
        pageChange: function (page) { // 分页
            this.queryForm.page = page
            this.getData()
        },
        handleAddOrEdit: function (isAdd, row) { // 新增或更改数据库配置弹窗
            this.addEditDialogShow = true
            this.addEditDialogTitle = isAdd ? '新增数据库' : '编辑数据库'
            Form.clearError(this, 'addEditForm')
            if (isAdd) {
                var addEditForm = Utils.copy(defaultAddForm);
                addEditForm.env = this.databaseEnvs[0].value || ''
                addEditForm.type = this.databaseTypes[0].value || ''
                this.addEditForm = addEditForm;
            } else {
                this.addEditForm = Utils.copy(row)
            }
        },
        testConnect: function() {
            var that = this
            Form.validate(this, 'addEditForm', function () {
                Resource.post("${_contextPath_}/database/test_connect", that.addEditForm, function (resp) {
                    Message.success("连接成功")
                })
            })
        },
        doAddOrEdit: function () { // 执行新增或更改数据库配置
            var that = this
            var isEdit = !!this.addEditForm.id
            Form.validate(this, 'addEditForm', function () {
                Resource.post("${_contextPath_}/database/add_or_update", that.addEditForm, function (resp) {
                    Message.success(isEdit ? "修改成功" : "新增成功")
                    isEdit ? (that.addEditDialogShow = false) : that.addEditForm = Utils.copy(defaultAddForm)
                    that.getData()
                })
            })
        },
        handleDelete: function () { // 删除数据库配置
            var that = this
            Message.confirm("确定要删除吗?", function () {
                Resource.get("${_contextPath_}/database/delete", {id: that.addEditForm.id}, function () {
                    Message.success("删除成功，列表已刷新")
                    that.addEditDialogShow = false
                    that.getData()
                })
            })
        },
        handleShowPassword: function() {
            this.showPassword = !this.showPassword
        },
        doShowTableSizeInfo: function(row) {
            this.currentDatabaseId = row.id
            this.showTableSizeInfo = true
        },
        doShowProcessList: function(row) {
            this.currentDatabaseId = row.id
            this.showProcessList = true
        },
        doShowSystemVariables: function(row) {
            this.currentDatabaseId = row.id
            this.showSystemVariables = true
        },
        enableMonitor: function($event, databaseId) {
            var that = this
            function doSwitchMonitor() {
                Resource.post("${_contextPath_}/database/enable_monitor", {
                    databaseId: databaseId, enabled: $event
                }, function(resp) {
                    Message.success("修改成功")
                    that.getData()
                })
            }

            if ($event) {
                Resource.get("${_contextPath_}/database/get_build_in_monitor_count", {
                    databaseId: databaseId
                }, function(resp) {
                    if (resp.data > 0) {
                        Message.confirm("已存在内置监控，重新开启将会覆盖，是否继续？", function() {
                            doSwitchMonitor()
                        }, function() {that.getData()})
                    } else {
                        doSwitchMonitor()
                    }
                })
            } else {
                doSwitchMonitor()
            }
        },
        enableCapacityMonitor: function($event, databaseId) {
            var that = this
            Resource.post("${_contextPath_}/database/enable_capacity_monitor", {
                databaseId: databaseId, enabled: $event
            }, function(resp) {
                Message.success("修改成功，开启容量监控请记得设置数据库容量上限")
                that.getData()
            })
        },
        resetConnectionPool: function() {
            var that = this
            Resource.post("${_contextPath_}/database/reset_connection_pool", {}, function () {
                Message.success("重置成功")
                that.getData()
            })
        }
    }
})
</script>
