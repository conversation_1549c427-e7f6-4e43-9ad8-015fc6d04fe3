<style>
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item label="搜索内容">
            <el-input v-model="queryForm.searchReg" placeholder="正则表达式"></el-input>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="startSearch()">搜索</el-button>
            <el-button type="success" @click="resetQuery()" v-show="progress < 100 && search">停止</el-button>
        </el-form-item>
        <div style="display: inline-block;width: 50%;line-height: 45px">
            进度
            <el-progress :percentage="progress" :format="format" style="width: 40%;display: inline-block"></el-progress>
            <span>总数：{{tableData.length}}</span>
        </div>
    </el-form>

    <el-table :data="tableData" border stripe>
        <el-table-column prop="serialNumber" label="序号"></el-table-column>
        <el-table-column prop="repository" label="仓库名"></el-table-column>
        <el-table-column prop="branch" label="分支"></el-table-column>
        <el-table-column prop="codeLine" label="文件（代码行）" min-width="300px"></el-table-column>
        <el-table-column prop="content" label="内容" min-width="300px"></el-table-column>
    </el-table>
</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            rules: {},
            progress: 0, tableData: [],
            uuid: '', search: false,
            showDialog: false, dialogTitle: ''
        },
        created: function () {
            setInterval(this.getData, 3000)
        },
        methods: {
            getData: function () {
                var that = this
                if (that.search) {
                    Resource.post("${_contextPath_}/search_code/get_page", {uuid: that.uuid}, function (resp) {
                        that.tableData = resp.data.data
                        that.progress = resp.data.progress
                        if (resp.data.progress == 100) {
                            that.search = false;
                        }
                    })
                }
            },
            startSearch: function () {
                var that = this
                Resource.post("${_contextPath_}/search_code/start_search", this.queryForm, function (resp) {
                    that.uuid = resp.data
                    that.search = true
                    that.tableData = []
                    that.progress = 0
                })

            },
            resetQuery: function () {
                var that = this
                that.progress = 0
                that.search = false
            },
            format: function (percentage) {
                return this.progress + "%";
            }
        }
    })
</script>