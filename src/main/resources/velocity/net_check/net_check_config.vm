#set($page_title='网络检查配置')
#parse("net_check/net_check_result.vm")

<style>
    .net-check-config label {width: 150px;color: #99a9bf;}
    .net-check-config .el-form-item {width: 100%;}
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item label="编号">
            <el-input v-model="queryForm.id" placeholder="仅示例，后台未实现"></el-input>
        </el-form-item>
        <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
        <el-form-item>
            <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
            <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column type="expand">
            <template slot-scope="props">
                <el-form :data="props" class="net-check-config">
                    <el-form-item label="参数">{{props.row.params}}</el-form-item>
                    <el-form-item label="连接超时(毫秒)">{{props.row.connectTimeoutMs}}</el-form-item>
                    <el-form-item label="读取超时(毫秒)">{{props.row.readTimeoutMs}}</el-form-item>
                    <el-form-item label="执行次数">{{props.row.times}}</el-form-item>
                    <el-form-item label="断言表达式">{{props.row.assertion}}</el-form-item>
                    <el-form-item label="成功记录频率(秒)">{{props.row.succLogRateSecs}}</el-form-item>
                    <el-form-item label="备注">{{props.row.comment}}</el-form-item>
                    <el-form-item label="上次错误时间">{{props.row.lastErrorTime}}</el-form-item>
                    <el-form-item label="成功次数">{{props.row.countSuccess}}</el-form-item>
                    <el-form-item label="错误次数">{{props.row.countError}}</el-form-item>
                    <el-form-item label="告警邮件">{{props.row.sendEmail}}</el-form-item>
                    <el-form-item label="创建时间">{{props.row.createTime}}</el-form-item>
                    <el-form-item label="更新时间">{{props.row.updateTime}}</el-form-item>
                    <el-form-item label="创建用户">{{props.row.createUserId}}</el-form-item>
                    <el-form-item label="更新用户">{{props.row.updateUserId}}</el-form-item>
                </el-form>
            </template>
        </el-table-column>
        <el-table-column prop="id" label="编号"></el-table-column>
        <el-table-column prop="name" label="名称"></el-table-column>
        <el-table-column prop="category" label="分类"></el-table-column>
        <el-table-column prop="netType" label="网络类型"></el-table-column>
        <el-table-column prop="method" label="请求方式"></el-table-column>
        <el-table-column prop="uri" label="请求地址"></el-table-column>
        <el-table-column prop="rateSeconds" label="检查频率(秒)"></el-table-column>
        <el-table-column prop="cronExpression" label="定时表达式"></el-table-column>
        <el-table-column label="是否启用" width="80">
            <template slot-scope="props">
                <el-switch v-model="props.row.isEnabled" active-color="#13ce66" inactive-color="#BEBEBE"
                           @change="enableCheck($event, props.row.id)">
                </el-switch>
            </template>
        </el-table-column>
        <el-table-column label="最后执行结果" width="120">
            <template slot-scope="props">
                <span :style="{ color: props.row.isLastSuccess ? '#67C23A' : '#F56C6C' }">
                    {{ props.row.isLastSuccess ? '成功' : '失败' }}
                </span>
            </template>
        </el-table-column>
        <el-table-column label="最后执行时间">
            <template slot-scope="props">
                <el-tooltip class="item" effect="dark" :content="props.row.lastTime" placement="bottom">
                    <span>{{props.row.lastTimeStr}}</span>
                </el-tooltip>
            </template>
        </el-table-column>
        <el-table-column label="操作">
            <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                <el-button type="plain" size="small" @click="showResult(scope.row)">结果</el-button>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
            <el-form-item label="名称" prop="name">
                <el-input v-model="addEditForm.name" placeholder="检查名称"></el-input>
            </el-form-item>
            <el-form-item label="分类" prop="category">
                <el-input v-model="addEditForm.category" placeholder="分类，只是一个标识"></el-input>
            </el-form-item>
            <el-form-item label="网络类型" prop="netType">
                <el-input v-model="addEditForm.netType" placeholder="网络类型，如HTTP、TCP、PING"></el-input>
            </el-form-item>
            <el-form-item label="请求方式" prop="method">
                <el-input v-model="addEditForm.method" placeholder="请求方式，例如http的话有GET POST"></el-input>
            </el-form-item>
            <el-form-item label="请求地址" prop="uri">
                <el-input v-model="addEditForm.uri" placeholder="请求url或唯一连接标识"></el-input>
            </el-form-item>
            <el-form-item label="参数" prop="params">
                <el-input v-model="addEditForm.params" placeholder="请求体，默认用json格式，实际情况根据net_type来"></el-input>
            </el-form-item>
            <el-form-item label="连接超时(毫秒)" prop="connectTimeoutMs">
                <el-input v-model="addEditForm.connectTimeoutMs" placeholder="连接超时时间，单位毫秒"></el-input>
            </el-form-item>
            <el-form-item label="读取超时(毫秒)" prop="readTimeoutMs">
                <el-input v-model="addEditForm.readTimeoutMs" placeholder="读取超时时间，单位毫秒"></el-input>
            </el-form-item>
            <el-form-item label="执行次数" prop="times">
                <el-input v-model="addEditForm.times" placeholder="执行次数，空值默认为1次"></el-input>
            </el-form-item>
            <el-form-item label="断言表达式" prop="assertion">
                <el-input v-model="addEditForm.assertion" placeholder="检查断言表达式，表达式入参由net_type而定"></el-input>
            </el-form-item>
            <el-form-item label="检查频率(秒)" prop="rateSeconds">
                <el-input v-model="addEditForm.rateSeconds" placeholder="检查频率"></el-input>
            </el-form-item>
            <el-form-item label="定时表达式" prop="cronExpression">
                <el-input v-model="addEditForm.cronExpression" placeholder="定时任务表达式"></el-input>
            </el-form-item>
            <el-form-item label="成功记录频率(秒)" prop="succLogRateSecs">
                <el-input v-model="addEditForm.succLogRateSecs" placeholder="当成功时，写入的频率"></el-input>
            </el-form-item>
            <el-form-item label="是否启用" prop="isEnabled">
                <el-switch v-model="addEditForm.isEnabled" active-color="#13ce66" inactive-color="#BEBEBE">
                </el-switch>
            </el-form-item>
            <el-form-item label="备注" prop="comment">
                <el-input v-model="addEditForm.comment" placeholder="备注"></el-input>
            </el-form-item>
            <el-form-item label="告警邮件" prop="sendEmail">
                <el-input v-model="addEditForm.sendEmail" placeholder="分号隔开，错误时发送邮件；复用后也支持钉钉"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

    <!-- 加v-if让对话框销毁，这样里面内容下次打开时可以刷新-->
    <el-dialog :title="'检查结果 - ' + currentRow.name" :visible.sync="showResultDialog" width="80%" top="10px" v-if="showResultDialog">
        <net-check-result :config-id="currentRow.id"></net-check-result>
    </el-dialog>

</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {isEnabled: true}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            rules: {/*name: Form.notBlankValidator('名称不能为空')*/},
            total: 0, tableData: [], tableLoading: false,
            showDialog: false, dialogTitle: '',
            showResultDialog: false,
            currentRow: {}
        },
        created: function() {
            this.getData()
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/net_check_config/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/net_check_config/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增net检查配置表' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/net_check_config/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            },
            enableCheck: function($event, configId) {
                var that = this
                Resource.post("${_contextPath_}/net_check_config/enable_disable_config", {
                    configId: configId, enabled: $event
                }, function(resp){
                    Message.success("修改成功")
                    that.getData()
                })
            },
            showResult: function(row) {
                this.currentRow = row;
                this.showResultDialog = true;
            }
        }
    })
</script>