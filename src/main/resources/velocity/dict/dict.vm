#set($page_title='字典管理')

#parse("dict/dict_column.vm")

<style>
    .table-expand label {width: 140px;color: #99a9bf;}
    .table-expand .el-form-item {width: 100%;}
</style>

<div id="app" v-cloak>
    <el-form :inline="true" @keyup.native.enter="getData">
        <el-form-item label="字典名称">
            <el-input v-model="queryForm.name" placeholder="支持模糊查询"></el-input>
        </el-form-item>
        <el-input style="display: none"></el-input>
        <el-form-item>
            <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
            <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
        </el-form-item>
    </el-form>

    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column type="expand">
            <template slot-scope="props">
                <el-form :data="props" class="table-expand">
                    <el-form-item label="额外的where条件">{{props.row.extraWhere}}</el-form-item>
                </el-form>
            </template>
        </el-table-column>
        <el-table-column prop="id" label="id" width="50"></el-table-column>
        <el-table-column prop="name" label="字典名称" width="160"></el-table-column>
        <el-table-column prop="namespace" label="命名空间" width="100"></el-table-column>
        <el-table-column prop="description" label="描述"></el-table-column>
        <el-table-column prop="database" label="数据库实例" width="120"></el-table-column>
        <el-table-column prop="databaseName" label="数据库名" width="120"></el-table-column>
        <el-table-column prop="tableName" label="表名"></el-table-column>
        <el-table-column prop="refUrl" label="相关链接"></el-table-column>
        <el-table-column label="操作">
            <template slot-scope="scope">
                <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
                <el-button type="plain" size="small" @click="doShowDictColumn(scope.row)">字典列</el-button>
            </template>
        </el-table-column>
    </el-table>

    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
        <el-form :model="addEditForm" label-position="right" label-width="150px" :rules="rules" ref="addEditForm">
            <el-form-item label="字典名称" prop="name">
                <el-input v-model="addEditForm.name" placeholder="字典名称"></el-input>
            </el-form-item>
            <el-form-item label="命名空间" prop="namespace">
                <el-input v-model="addEditForm.namespace" placeholder="字典表的命名空间，支持中文，这个是为了区分具有相同名称字段的字典，它和name还是有区别，同一个namespace下不应该有相同的字段"></el-input>
            </el-form-item>
            <el-form-item label="描述" prop="description">
                <el-input v-model="addEditForm.description" placeholder="字典描述信息"></el-input>
            </el-form-item>
            <el-form-item label="数据库实例id" prop="databaseId">
                <el-select v-model="addEditForm.databaseId" placeholder="请选择">
                    <el-option v-for="item in databases" :key="item.databaseId" :label="item.name" :value="item.databaseId">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="数据库名" prop="databaseName">
                <el-input v-model="addEditForm.databaseName" placeholder="数据库名，可选"></el-input>
            </el-form-item>
            <el-form-item label="表名" prop="tableName">
                <el-input v-model="addEditForm.tableName" placeholder="字典对应的表名，可选"></el-input>
            </el-form-item>
            <el-form-item label="额外的where条件" prop="extraWhere">
                <el-input v-model="addEditForm.extraWhere" placeholder="可选，例如where deleted=0"></el-input>
            </el-form-item>
            <el-form-item label="相关链接" prop="refUrl">
                <el-input v-model="addEditForm.refUrl" placeholder="参考的链接"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer">
            <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id">删除</el-button>
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="doAddOrEdit">确定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="字典列" :visible.sync="showDictColumn" top="10px" width="1500" v-if="showDictColumn">
        <dict-column :dict-id="currentDictId"></dict-column>
    </el-dialog>

</div>

<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    var vm = new Vue({
        el: '#app',
        data: {
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            rules: {},
            total: 0, tableData: [], tableLoading: false,
            showDialog: false, dialogTitle: '',
            databases: [], // 数据库下拉选择
            showDictColumn: false,
            currentDictId: 0
        },
        created: function() {
            var that = this
            this.getData()
            Resource.get("${_contextPath_}/database/get_database_for_select", {}, function(resp){
                that.databases = resp.data
            })
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                Resource.get("${_contextPath_}/dict/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/dict/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.showDialog = true
                this.dialogTitle = isAdd ? '新增字典表' : '编辑'
                Form.clearError(this, 'addEditForm')
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  this.addEditForm.id ? true : false
                Form.validate(this, 'addEditForm', function() {
                    Resource.post("${_contextPath_}/dict/add_or_update", that.addEditForm, function(resp){
                        Message.success(isEdit ? "修改成功" : "新增成功")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                    })
                })
            },
            doShowDictColumn: function(row) {
                this.currentDictId = row.id
                this.showDictColumn = true
            }
        }
    })
</script>