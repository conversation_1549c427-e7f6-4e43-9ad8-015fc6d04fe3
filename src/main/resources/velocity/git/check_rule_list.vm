#set($page_title='扫描配置管理')
<style>
    a {
        text-decoration: none
    }
</style>
<div id="app" v-cloak>
##    <el-form :inline="true" @keyup.native.enter="getData">
##        <el-form-item label="">
##            <el-input v-model="queryForm.name" placeholder="规则名称"></el-input>
##        </el-form-item>
##        <el-form-item>
##            <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
##            <el-button @click="resetQuery">重置</el-button>
##        </el-form-item>
##    </el-form>
    <p>目前仅支持查询，请在数据库配置</p>
    <el-table :data="tableData" border stripe v-loading.body="tableLoading">
        <el-table-column label="规则名" prop="name" min-width="60"></el-table-column>
        <el-table-column label="规则详情" min-width="130">
            <template slot-scope="props">
                <ul>
                    <li style="display: flex;justify-content: space-between;margin-top: 4px;"
                        v-for="o in props.row.rules">
                        <div>【{{o.level}}】{{o.type}} {{o.content}}</div>
                    </li>
                </ul>
            </template>
        </el-table-column>
        <el-table-column label="文件名正则表达式" prop="files" min-width="200">
            <template slot-scope="props">
                <ul>
                    <li style="display: flex;justify-content: space-between;margin-top: 4px;"
                        v-for="o in props.row.files">
                        <div>【{{o.name}}】{{o.regex}}</div>
                    </li>
                </ul>
            </template>
        </el-table-column>
    </el-table>
    <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                   :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
    </el-pagination>
</div>
<script>
var defaultQueryForm = {page: 1, pageSize: 50}
var defaultAddForm = {}
var vm = new Vue({
    el: '#app',
    data: {
        queryForm: Utils.copy(defaultQueryForm),
        total: 0,
        tableData: [],
        tableLoading: false,
    },
    created: function () {
        this.getData()
    },
    methods: {
        getData: function () {
            var that = this
            that.tableLoading = true
            Resource.post("${_contextPath_}/check_rule/get_page", this.queryForm, function (resp) {
                that.tableData = resp.data.data
                that.total = resp.data.total
                that.tableLoading = false
            })
        },
        pageChange: function (page) {
            this.queryForm.page = page
            this.getData()
        },
        resetQuery: function () {
            this.queryForm = Utils.copy(defaultQueryForm)
        }
    }
})
</script>