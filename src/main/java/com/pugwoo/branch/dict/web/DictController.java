package com.pugwoo.branch.dict.web;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.dict.entity.DictDO;
import com.pugwoo.branch.dict.service.DictService;
import com.pugwoo.branch.dict.vo.DictVO;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

@RestController
@RequestMapping(value = "/dict")
public class DictController {

    @Autowired
    private DictService dictService;
    
    @GetMapping("list")
    public ModelAndView list() {
        return new ModelAndView("dict/dict");
    }

    @GetMapping("get_page")
    public WebJsonBean<Map<String, Object>> getPage(int page, int pageSize, String name) {
        PageData<DictVO> pageData = dictService.getPage(page, pageSize, name);
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }
    
    @PostMapping("add_or_update")
    public WebJsonBean<Long> addOrUpdate(DictDO dictDO) {
        WebCheckUtils.assertNotNull(dictDO, "缺少修改的对象参数");

        // TODO check parameters

        ResultBean<Long> result = dictService.insertOrUpdate(dictDO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @PostMapping("delete")
    public WebJsonBean<Boolean> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(dictService.deleteById(id));
    }

}
