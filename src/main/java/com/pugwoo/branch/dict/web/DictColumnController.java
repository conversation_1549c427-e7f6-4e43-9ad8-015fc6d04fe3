package com.pugwoo.branch.dict.web;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.dict.entity.DictColumnDO;
import com.pugwoo.branch.dict.service.DictColumnService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/dict_column")
public class DictColumnController {

    @Autowired
    private DictColumnService dictColumnService;

    @GetMapping("get_all")
    public List<DictColumnDO> getAll(Long dictId, String name) {
        return dictColumnService.getAll(dictId, name);
    }
    
    @PostMapping("add_or_update")
    public WebJsonBean<Long> addOrUpdate(DictColumnDO dictColumnDO) {
        WebCheckUtils.assertNotNull(dictColumnDO, "缺少修改的对象参数");
        WebCheckUtils.assertNotNull(dictColumnDO.getDictId(), "dictId必须提供");

        if (dictColumnDO.getIsVirtualColumn() == null) {
            dictColumnDO.setIsVirtualColumn(false);
        }

        ResultBean<Long> result = dictColumnService.insertOrUpdate(dictColumnDO);
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @PostMapping("delete")
    public WebJsonBean<Boolean> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(dictColumnService.deleteById(id));
    }

}
