package com.pugwoo.branch.dict.vo;

import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.dict.entity.DictDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

@Data
public class DictVO extends DictDO {

    @RelatedColumn(localColumn = "database_id", remoteColumn = "id")
    private DatabaseDO databaseDO;

    /**vm用到*/
    public String getDatabase() {
        return databaseDO == null ? "" : databaseDO.getName();
    }

}
