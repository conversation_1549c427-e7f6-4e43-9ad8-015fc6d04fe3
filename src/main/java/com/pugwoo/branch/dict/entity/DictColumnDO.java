package com.pugwoo.branch.dict.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 字典列
 */
@Data
@ToString
@Table("dict_column")
public class DictColumnDO extends AdminCoreDO {

    /** 所属的字典id<br/>Column: [dict_id] */
    @Column(value = "dict_id")
    private Long dictId;

    /** 列名，这个会作为外部引用，中文<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 数据库中的列名<br/>Column: [db_column_name] */
    @Column(value = "db_column_name")
    private String dbColumnName;

    /** 列类型，String、Date、DateTime、Integer、Decimal等<br/>Column: [column_type] */
    @Column(value = "column_type")
    private String columnType;

    /** 描述信息<br/>Column: [description] */
    @Column(value = "description")
    private String description;

    /** 是否虚拟列，虚拟列就是数据库不存在的列<br/>Column: [is_virtual_column] */
    @Column(value = "is_virtual_column")
    private Boolean isVirtualColumn;

    /** 匹配表达式，一般只有虚拟列才需要，例如 ?>=start and ?<=end这样，sql语法，问号是占位符<br/>Column: [match_expression] */
    @Column(value = "match_expression")
    private String matchExpression;

    /** 展示的表达式，sql语法，一般是虚拟列才需要这个表达式<br/>Column: [display_expression] */
    @Column(value = "display_expression")
    private String displayExpression;

}