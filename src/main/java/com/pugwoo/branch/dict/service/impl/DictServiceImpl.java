package com.pugwoo.branch.dict.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.dict.entity.DictDO;
import com.pugwoo.branch.dict.service.DictService;
import com.pugwoo.branch.dict.vo.DictVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DictServiceImpl implements DictService {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public DictDO getById(Long id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(DictDO.class, id);
    }

    @Override
    public PageData<DictVO> getPage(int page, int pageSize, String name) {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.andIf(StringTools.isNotBlank(name), "name like ?", "%" + name + "%");
        return dbHelper.getPage(DictVO.class, page, pageSize, whereSQL.getSQL(), whereSQL.getParams());
    }

    @Override
    public ResultBean<Long> insertOrUpdate(DictDO dictDO) {
        if(dictDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(dictDO);
        return rows > 0 ? ResultBean.ok(dictDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        DictDO dictDO = new DictDO();
        dictDO.setId(id);
        return dbHelper.delete(dictDO) > 0;
    }

}