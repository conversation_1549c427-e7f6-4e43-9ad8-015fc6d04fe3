package com.pugwoo.branch.dict.service;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.dict.entity.DictColumnDO;

import java.util.List;

public interface DictColumnService {

    /**
     * 通过主键获得数据
     */
    DictColumnDO getById(Long id);
    
    /**
     */
    List<DictColumnDO> getAll(Long dictId, String name);
    
    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdate(DictColumnDO dictColumnDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

}