package com.pugwoo.branch.common;

import com.pugwoo.admin.bean.WebJsonBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;

/**
 * 处理数据库报错异常，这里允许直接向前端暴露SQL相关细节
 */
@Slf4j
@RestControllerAdvice
public class DatabaseExceptionHandler {

    @ExceptionHandler(SQLException.class)
    public WebJsonBean gitApiException(SQLException ex) {
        String msg = ex.getMessage();
        return WebJsonBean.fail("数据库异常:" + msg);
    }

}
