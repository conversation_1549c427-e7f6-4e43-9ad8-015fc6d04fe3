package com.pugwoo.branch.nimble_orm.utils;

import com.pugwoo.branch.nimble_orm.model.GenerateTableVO;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

import java.io.StringWriter;

public class SQLRenderUtils {

    public static String generateDOClassJavaCode(GenerateTableVO generateTableVO) {
        return renderVM("/dbhelper/dbhelper_do.vm", generateTableVO);
    }

    public static String generateIService(GenerateTableVO generateTableVO) {
        return renderVM("/dbhelper/dbhelper_i_service.vm", generateTableVO);
    }

    public static String generateServiceImpl(GenerateTableVO generateTableVO) {
        return renderVM("/dbhelper/dbhelper_service_impl.vm", generateTableVO);
    }

    public static String generateControllerSimplify(GenerateTableVO generateTableVO) {
        return renderVM("/dbhelper/dbhelper_controller_simplify.vm", generateTableVO);
    }

    public static String generateControllerVue(GenerateTableVO generateTableVO) {
        return renderVM("/dbhelper/dbhelper_controller_vue.vm", generateTableVO);
    }

    public static String generateVm(GenerateTableVO generateTableVO) {
        return renderVM("/dbhelper/dbhelper_vm.vm", generateTableVO);
    }

    public static String generateVmVueComponent(GenerateTableVO generateTableVO) {
        return renderVM("/dbhelper/dbhelper_vm_vue_component.vm", generateTableVO);
    }

    public static String generateClickhouse(GenerateTableVO generateTableVO) {
        return renderVM("/dbhelper/dbhelper_clickhouse.vm", generateTableVO);
    }

    private static String renderVM(String templatePath, GenerateTableVO generateTableVO) {
        VelocityEngine ve = new VelocityEngine();
        ve.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
        ve.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
        ve.init();
        Template t = ve.getTemplate(templatePath, "utf-8");

        VelocityContext context = new VelocityContext();
        context.put("table", generateTableVO);
        context.put("columns", generateTableVO.getColumns());
        context.put("imports", generateTableVO.getJavaImportClasses());

        StringWriter writer = new StringWriter();
        t.merge(context, writer);

        return writer.toString();
    }

}
