package com.pugwoo.branch.nimble_orm.utils;

import com.pugwoo.wooutils.string.StringTools;

import java.util.List;
import java.util.Set;

/**
 * 类型转换相关的服务
 */
public class DataTypeUtils {


    public static String dbToJavaType(String dataType, Set<String> javaImports,
                                      boolean isUnsigned, List<String> argumentsStringList) {

        // 1. 优先mysql
        String type = mysqlToJavaType(dataType, javaImports, isUnsigned, argumentsStringList);
        if (type != null) {
            return type;
        }

        // 2. 尝试ck
        type = clickhouseToJavaType(dataType, javaImports, isUnsigned, argumentsStringList);
        if (type != null) {
            return type;
        }

        return "UNKNOWN";
    }

    public static String mysqlToClickhouseType(String dataType,
                                               boolean isUnsigned, List<String> argumentsStringList) {

        if ("INT".equalsIgnoreCase(dataType)) {
            return isUnsigned ? "UInt32" : "Int32";
        }
        if ("smallint".equalsIgnoreCase(dataType)) {
            return isUnsigned ? "UInt16" : "Int16";
        }
        if (StringTools.isInIgnoreCase(dataType, "MEDIUMINT")) {
            return "Int32";
        }
        if ("BIGINT".equalsIgnoreCase(dataType)) {
            return isUnsigned ? "UInt64" : "Int64";
        }
        if ("TINYINT".equalsIgnoreCase(dataType)) {
            // 按mysql的规范，TINYINT(1)才是boolean类型
            if (argumentsStringList != null && argumentsStringList.size() > 0
                    && "1".equals(argumentsStringList.get(0))) {
                return "Bool";
            } else {
                return isUnsigned ? "UInt8" : "Int8";
            }
        }
        if ("DOUBLE".equalsIgnoreCase(dataType)) {
            return "Float64";
        }
        if ("FLOAT".equalsIgnoreCase(dataType)) {
            return "Float32";
        }
        if (StringTools.isInIgnoreCase(dataType, "DECIMAL")) {
            if (argumentsStringList != null && argumentsStringList.size() == 2) {
                return "Decimal(" + argumentsStringList.get(0) + "," + argumentsStringList.get(1) + ")";
            } else {
                return "Decimal(P,S)";
            }
        }
        if (StringTools.isInIgnoreCase(dataType, "VARCHAR", "CHAR",
                "ENUM", "SET", "longtext", "mediumtext", "text", "tinytext", "json")) {
            return "String";
        }
        if (StringTools.isInIgnoreCase(dataType,
                "BINARY", "VARBINARY", "BIT", "TINYBLOB", "BLOB", "MEDIUMBLOB", "LONGBLOB")) {
            return "BINARY";
        }
        if (StringTools.isInIgnoreCase(dataType, "DATE")) {
            return "Date";
        }
        if (StringTools.isInIgnoreCase(dataType, "year")) {
            return "UInt16";
        }
        if (StringTools.isInIgnoreCase(dataType, "DATETIME", "TIME")) {
            return "DateTime";
        }
        if ("timestamp".equalsIgnoreCase(dataType)) {
            return "DateTime64";
        }

        return dataType; // 找不到的不转
    }

    /**
     * mysql数据库类型转Java类型
     */
    private static String mysqlToJavaType(String dataType, Set<String> javaImports,
                                          boolean isUnsigned, List<String> argumentsStringList) {

        if ("INT".equalsIgnoreCase(dataType)) {
            return isUnsigned ? "Long" : "Integer";
        }
        if (StringTools.isInIgnoreCase(dataType, "smallint", "MEDIUMINT")) {
            return "Integer";
        }
        if ("BIGINT".equalsIgnoreCase(dataType)) {
            return "Long";
        }
        if ("TINYINT".equalsIgnoreCase(dataType)) {
            // 按mysql的规范，TINYINT(1)才是boolean类型
            if (argumentsStringList != null && argumentsStringList.size() > 0
                    && "1".equals(argumentsStringList.get(0))) {
                return "Boolean";
            } else {
                return "Integer";
            }
        }
        if ("DOUBLE".equalsIgnoreCase(dataType)) {
            return "Double";
        }
        if ("FLOAT".equalsIgnoreCase(dataType)) {
            return "Float";
        }
        if (StringTools.isInIgnoreCase(dataType, "DECIMAL")) {
            javaImports.add("java.math.BigDecimal");
            return "BigDecimal";
        }
        if (StringTools.isInIgnoreCase(dataType, "VARCHAR", "CHAR",
                "ENUM", "SET", "longtext", "mediumtext", "text", "tinytext", "json")) {
            return "String";
        }
        if (StringTools.isInIgnoreCase(dataType,
                "BINARY", "VARBINARY", "BIT", "TINYBLOB", "BLOB", "MEDIUMBLOB", "LONGBLOB")) {
            return "byte[]";
        }
        if (StringTools.isInIgnoreCase(dataType, "DATE")) {
            javaImports.add("java.time.LocalDate");
            return "LocalDate";
        }
        if (StringTools.isInIgnoreCase(dataType, "year")) {
            return "Integer";
        }
        if (StringTools.isInIgnoreCase(dataType, "DATETIME", "timestamp")) {
            javaImports.add("java.time.LocalDateTime");
            return "LocalDateTime";
        }
        if (StringTools.isInIgnoreCase(dataType, "TIME")) {
            javaImports.add("java.time.LocalTime");
            return "LocalTime";
        }

        return null;
    }

    /**
     * clickhouse数据库类型转Java类型
     */
    private static String clickhouseToJavaType(String dataType, Set<String> javaImports, boolean isUnsigned,
                                          List<String> argumentsStringList) {

        if (StringTools.isInIgnoreCase(dataType, "Int8", "Int16", "Int32", "UInt8", "UInt16")) {
            return "Integer";
        }
        if (StringTools.isInIgnoreCase(dataType, "Int64", "UInt32")) {
            return "Long";
        }
        if (StringTools.isInIgnoreCase(dataType, "Int128", "Int256", "UInt64", "UInt128", "UInt256")) {
            javaImports.add("java.math.BigDecimal");
            return "BigDecimal";
        }
        if (StringTools.isInIgnoreCase(dataType, "Float32")) {
            return "Float";
        }
        if (StringTools.isInIgnoreCase(dataType, "Float64")) {
            return "Double";
        }
        if (dataType.startsWith("Decimal") || dataType.startsWith("decimal")) {
            javaImports.add("java.math.BigDecimal");
            return "BigDecimal";
        }
        if (StringTools.isInIgnoreCase(dataType, "bool", "Boolean")) {
            return "Boolean";
        }
        if (StringTools.isInIgnoreCase(dataType, "String", "UUID", "Enum")
                || dataType.startsWith("FixedString")) {
            return "String";
        }
        if (StringTools.isInIgnoreCase(dataType, "Date", "Date32")) {
            javaImports.add("java.time.LocalDate");
            return "LocalDate";
        }
        if (StringTools.isInIgnoreCase(dataType, "DateTime")) {
            javaImports.add("java.time.LocalDateTime");
            return "LocalDateTime";
        }
        if (StringTools.isInIgnoreCase(dataType, "DateTime64")) {
            javaImports.add("java.time.LocalDateTime");
            return "LocalDateTime";
        }

        return null;
    }
}
