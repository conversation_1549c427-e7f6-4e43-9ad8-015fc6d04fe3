package com.pugwoo.branch.nimble_orm.model;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class GenerateTableVO {

	// sql的类型，SELECT或CREATE
	private String sqlType;
	
	// 表名
	private String name;
	
	// java类名
	private String className;
	
	// java类名，但是第一个字母小写
	private String lowClassName;
	
	// java类名，全部小写，下划线形式，例如hello_world
	private String allLowClassName;

	// 全部小写，横线形式，例如hello-world
	private String allLowDashClassName;
	
	// 表备注
	private String comment;
	
	// 列
	private List<GenerateColumnVO> columns;
	
	// 主键列的个数
	private int keyColumnNum;
	
	// java import 类
	private Set<String> javaImportClasses;
	
	// 是否继承BaseDO
	private boolean isUseBaseDO = false;

	/**BaseDO基类的名称*/
	private String baseClassSimpleName = "";

}
