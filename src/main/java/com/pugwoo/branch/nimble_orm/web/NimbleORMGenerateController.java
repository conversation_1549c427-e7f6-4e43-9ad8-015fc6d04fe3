package com.pugwoo.branch.nimble_orm.web;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.NotRequireLogin;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContext;
import com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor;
import com.pugwoo.branch.nimble_orm.entity.NimbleOrmGenLogDO;
import com.pugwoo.branch.nimble_orm.model.GenerateColumnVO;
import com.pugwoo.branch.nimble_orm.model.GenerateTableVO;
import com.pugwoo.branch.nimble_orm.utils.PreHandleSQLUtils;
import com.pugwoo.branch.nimble_orm.utils.SQLParserUtils;
import com.pugwoo.branch.nimble_orm.utils.SQLRenderUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.CookieUtils;
import com.pugwoo.wooutils.string.StringTools;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@NotRequireLogin
@Controller
public class NimbleORMGenerateController {

	private final ThreadPoolExecutor executeThem = ThreadPoolUtils.createThreadPool(1,
			10000, 1, "write-gen-log");

	@Autowired
	private DBHelper dbHelper;

	@RequestMapping("/nimble-orm-gen")
	public String gen(Model model, HttpServletRequest request) {
		AdminUserLoginContext login = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
		model.addAttribute("isLogin", login != null);

		String baseDOName = CookieUtils.getCookieValueForJakarta(request, "baseDOName");
		if (StringTools.isBlank(baseDOName)) {
			baseDOName = "com.pugwoo.admin.bean.AdminCoreDO";
		}
		model.addAttribute("baseDOName", baseDOName);

		return "nimble-orm-gen";
	}
	
	@ResponseBody @RequestMapping("/nimble-orm-gen.json")
	public WebJsonBean<?> genJavaCode(String createTable, Boolean enableBaseDO, String baseDOClass,
									  HttpServletResponse response) {
		if(StringTools.isBlank(createTable)) {
			return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数createTable");
		}
		// 如果用户自定义了baseDOClass名称，则写入cookie
		if (StringTools.isNotBlank(baseDOClass) && !"com.pugwoo.admin.bean.AdminCoreDO".equals(baseDOClass)) {
			CookieUtils.addCookieForJakarta(response, "baseDOName", baseDOClass, null);
		}

		NimbleOrmGenLogDO nimbleOrmGenLogDO = new NimbleOrmGenLogDO();
		
		try {
			nimbleOrmGenLogDO.setCreateSql(createTable);

			// 先试着按原来的解析，不行再handle一次继续尝试解析（这些是因为处理可能把正常的处理错了，所以两种都试试）
			GenerateTableVO generateTableVO;
			try {
				generateTableVO = SQLParserUtils.parse(createTable, enableBaseDO, baseDOClass);

				// 如果解析出来列为空，那还是尝试处理下
				if (ListUtils.isEmpty(generateTableVO.getColumns())) {
					createTable = PreHandleSQLUtils.handle(createTable);
					generateTableVO = SQLParserUtils.parse(createTable, enableBaseDO, baseDOClass);
				}

			} catch (Exception ignored) {
				createTable = PreHandleSQLUtils.handle(createTable);
				generateTableVO = SQLParserUtils.parse(createTable, enableBaseDO, baseDOClass);
			}

			Map<String, String> map = new HashMap<>();

			AdminUserLoginContext login = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
			if (login != null) {
				map.put("iService", SQLRenderUtils.generateIService(generateTableVO));
				map.put("serviceImpl", SQLRenderUtils.generateServiceImpl(generateTableVO));
				map.put("controllerSimplify", SQLRenderUtils.generateControllerSimplify(generateTableVO));
				map.put("controllerVue", SQLRenderUtils.generateControllerVue(generateTableVO));
				map.put("vm", SQLRenderUtils.generateVm(generateTableVO));
				map.put("vmVueComponent", SQLRenderUtils.generateVmVueComponent(generateTableVO));
			}

			String doClass = SQLRenderUtils.generateDOClassJavaCode(generateTableVO);
			map.put("doClass", doClass);
			nimbleOrmGenLogDO.setGenDo(doClass);
			nimbleOrmGenLogDO.setIsSuccess(true);

			map.put("clickhouse", SQLRenderUtils.generateClickhouse(generateTableVO));

			List<String> checkDupColumn = checkDupColumn(generateTableVO);
			if (ListUtils.isNotEmpty(checkDupColumn)) {
				map.put("dupMessage", String.join("; ", checkDupColumn));
			} else {
				map.put("dupMessage", "");
			}

			return WebJsonBean.ok(map);
		} catch (JSQLParserException e) {
			nimbleOrmGenLogDO.setIsSuccess(false);
			nimbleOrmGenLogDO.setGenDo("解析出错:" + e.getCause());
			return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "解析出错:" + e.getCause());
		} finally {
			executeThem.submit(() -> {
				try {
					dbHelper.insert(nimbleOrmGenLogDO);
				} catch (Throwable e) {
					log.error("insert nimbleOrmGenLogDO fail:{}", JSON.toJson(nimbleOrmGenLogDO), e);
				}
			});
		}
	}

	/**检查解析出来的结果是否包含了相同的变量名或列名*/
	private List<String> checkDupColumn(GenerateTableVO generateTableVO) {
		List<GenerateColumnVO> columns = generateTableVO.getColumns();
		if (columns == null || columns.isEmpty()) {
			return null;
		}
		List<String> dupColumns = new ArrayList<>();
		List<String> dupVariables = new ArrayList<>();

		Set<String> columnSet = new HashSet<>();
		Set<String> variableSet = new HashSet<>();

		for (GenerateColumnVO column : columns) {
			if (!columnSet.contains(column.getName())) {
				columnSet.add(column.getName());
			} else {
				if (!dupColumns.contains(column.getName())) {
					dupColumns.add(column.getName());
				}
			}

			if (!variableSet.contains(column.getDownJavaVarName())) {
				variableSet.add(column.getDownJavaVarName());
			} else {
				if (!dupVariables.contains(column.getDownJavaVarName())) {
					dupVariables.add(column.getDownJavaVarName());
				}
			}
		}

		List<String> checkResult = new ArrayList<>();
		if (!dupColumns.isEmpty()) {
			checkResult.add("列名重复:" + String.join(", ", dupColumns));
		}
		if (!dupVariables.isEmpty()) {
			checkResult.add("变量名重复:" + String.join(", ", dupVariables));
		}
		return checkResult;
	}

}
