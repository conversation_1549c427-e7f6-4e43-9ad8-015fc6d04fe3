package com.pugwoo.branch.chart.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@ToString
@Table("web_sandbox")
public class WebSandboxDO extends AdminBaseDO {

    /** sandbox名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** sandbox链接code<br/>Column: [link_code] */
    @Column(value = "link_code")
    private String linkCode;

    /** 图表类型<br/>Column: [chart_type] */
    @Column(value = "chart_type")
    private String chartType;

    /** sandbox的html<br/>Column: [html] */
    @Column(value = "html")
    private String html;

    /** sandbox的css<br/>Column: [css] */
    @Column(value = "css")
    private String css;

    /** sandbox的js<br/>Column: [js] */
    @Column(value = "js")
    private String js;

    /** 最后访问时间<br/>Column: [last_visit_time] */
    @Column(value = "last_visit_time")
    private LocalDateTime lastVisitTime;

    /** 访问次数<br/>Column: [visit_count] */
    @Column(value = "visit_count")
    private Integer visitCount;

    /** 描述<br/>Column: [description] */
    @Column(value = "description")
    private String description;

}