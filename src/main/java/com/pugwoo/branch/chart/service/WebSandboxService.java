package com.pugwoo.branch.chart.service;

import com.pugwoo.branch.chart.entity.WebSandboxDO;
import com.pugwoo.branch.chart.web.req.CreateSandboxReq;

public interface WebSandboxService {

    WebSandboxDO getByLinkCode(String linkCode);

    /**
     * 创建沙盒，返回沙盒linkCode
     */
    String createWebSandbox(CreateSandboxReq createSandboxReq);

    /**
     * 记录访问
     */
    void recordVisit(WebSandboxDO webSandboxDO);

}
