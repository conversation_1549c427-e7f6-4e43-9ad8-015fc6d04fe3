package com.pugwoo.branch.chart.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.chart.entity.WebSandboxExampleDO;
import com.pugwoo.branch.chart.enums.ChartEnum;
import com.pugwoo.branch.chart.service.WebSandboxExampleService;
import com.pugwoo.branch.chart.service.WebSandboxService;
import com.pugwoo.branch.chart.web.req.CreateExampleReq;
import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.branch.chart.web.req.UpdateExampleReq;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class WebSandboxExampleServiceImpl implements WebSandboxExampleService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Resource
    private WebSandboxService webSandboxService;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public List<String> getAllLibraries() {
        return ListUtils.transform(ChartEnum.values(), ChartEnum::getLibrary).stream().distinct().toList();
    }

    @Override
    public List<String> getChartsByLibrary(String library) {
        if (StringTools.isBlank(library)) {
            return new ArrayList<>();
        }
        return Arrays.stream(ChartEnum.values())
                .filter(e -> e.getLibrary().equalsIgnoreCase(library))
                .flatMap(e -> e.getCharts().stream())
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    @Override
    public List<WebSandboxExampleDO> getExamplesByLibraryAndCharts(String library, String charts) {
        if (StringTools.isBlank(library) || StringTools.isBlank(charts)) {
            return new ArrayList<>();
        }
        return dbHelper.getAll(WebSandboxExampleDO.class, 
                "where library=? and charts=? order by create_time desc", 
                library, charts);
    }

    @Override
    public String getOrCreateLinkCode(String reqBody, boolean temporary) {
        if (StringTools.isBlank(reqBody)) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "请求体不能为空");
        }

        try {
            // 解析reqBody为CreateSandboxReq
            CreateSandboxReq createSandboxReq = objectMapper.readValue(reqBody, CreateSandboxReq.class);
            createSandboxReq.setTmp(temporary);
            
            // 如果不是临时的，先检查是否已存在相同reqBody的example
            if (!temporary) {
                WebSandboxExampleDO existingExample = dbHelper.getOne(WebSandboxExampleDO.class,
                        "where req_body=? and link_code is not null", reqBody);
                if (existingExample != null && StringTools.isNotBlank(existingExample.getLinkCode())) {
                    return existingExample.getLinkCode();
                }
            }
            
            // 创建新的linkCode
            String linkCode = webSandboxService.createWebSandbox(createSandboxReq);
            
            // 如果是永久的，更新数据库中对应的example记录
            if (!temporary && StringTools.isNotBlank(linkCode)) {
                WebSandboxExampleDO example = dbHelper.getOne(WebSandboxExampleDO.class,
                        "where req_body=?", reqBody);
                if (example != null) {
                    example.setLinkCode(linkCode);
                    dbHelper.update(example);
                }
            }
            
            return linkCode;
        } catch (JsonProcessingException e) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "请求体格式错误: " + e.getMessage());
        }
    }

    @Override
    public WebSandboxExampleDO createExample(CreateExampleReq req) {
        if (StringTools.isBlank(req.getName())) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "示例名称不能为空");
        }
        if (StringTools.isBlank(req.getLibrary())) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "库名称不能为空");
        }
        if (StringTools.isBlank(req.getCharts())) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "图表类型不能为空");
        }
        if (StringTools.isBlank(req.getReqBody())) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "请求体不能为空");
        }

        // 验证reqBody格式
        try {
            objectMapper.readValue(req.getReqBody(), CreateSandboxReq.class);
        } catch (JsonProcessingException e) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "请求体格式错误: " + e.getMessage());
        }

        WebSandboxExampleDO example = new WebSandboxExampleDO();
        example.setName(req.getName());
        example.setLibrary(req.getLibrary());
        example.setCharts(req.getCharts());
        example.setReqBody(req.getReqBody());
        example.setDescription(req.getDescription());

        // 生成永久linkCode
        String linkCode = getOrCreateLinkCode(req.getReqBody(), false);
        example.setLinkCode(linkCode);

        dbHelper.insert(example);
        return example;
    }

    @Override
    public WebSandboxExampleDO updateExample(UpdateExampleReq req) {
        if (req.getId() == null) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "ID不能为空");
        }

        WebSandboxExampleDO example = dbHelper.getByKey(WebSandboxExampleDO.class, req.getId());
        if (example == null) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "示例不存在");
        }

        if (StringTools.isNotBlank(req.getName())) {
            example.setName(req.getName());
        }
        if (StringTools.isNotBlank(req.getDescription())) {
            example.setDescription(req.getDescription());
        }
        if (StringTools.isNotBlank(req.getReqBody())) {
            // 验证reqBody格式
            try {
                objectMapper.readValue(req.getReqBody(), CreateSandboxReq.class);
            } catch (JsonProcessingException e) {
                throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "请求体格式错误: " + e.getMessage());
            }
            
            example.setReqBody(req.getReqBody());
            // 重新生成linkCode
            String linkCode = getOrCreateLinkCode(req.getReqBody(), false);
            example.setLinkCode(linkCode);
        }

        dbHelper.update(example);
        return example;
    }

    @Override
    public void deleteExample(Long id) {
        if (id == null) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "ID不能为空");
        }

        WebSandboxExampleDO example = dbHelper.getByKey(WebSandboxExampleDO.class, id);
        if (example == null) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "示例不存在");
        }

        dbHelper.delete(example);
    }

    @Override
    public WebSandboxExampleDO getExampleById(Long id) {
        if (id == null) {
            return null;
        }
        return dbHelper.getByKey(WebSandboxExampleDO.class, id);
    }

    @Override
    public Map<String, List<String>> getLibraryChartsMap() {
        Map<String, List<String>> result = new HashMap<>();
        for (ChartEnum chartEnum : ChartEnum.values()) {
            String library = chartEnum.getLibrary();
            result.computeIfAbsent(library, k -> new ArrayList<>())
                    .add(chartEnum.getCharts().getFirst());
        }
        return result;
    }
}
