package com.pugwoo.branch.chart.service;

import com.pugwoo.branch.chart.entity.WebSandboxExampleDO;
import com.pugwoo.branch.chart.web.req.CreateExampleReq;
import com.pugwoo.branch.chart.web.req.UpdateExampleReq;

import java.util.List;
import java.util.Map;

public interface WebSandboxExampleService {

    /**
     * 获取所有library列表
     */
    List<String> getAllLibraries();

    /**
     * 根据library获取charts列表
     */
    List<String> getChartsByLibrary(String library);

    /**
     * 根据library和charts获取examples
     */
    List<WebSandboxExampleDO> getExamplesByLibraryAndCharts(String library, String charts);

    /**
     * 根据reqBody获取或创建linkCode
     */
    String getOrCreateLinkCode(String reqBody, boolean temporary);

    /**
     * 创建新的example
     */
    WebSandboxExampleDO createExample(CreateExampleReq req);

    /**
     * 更新example
     */
    WebSandboxExampleDO updateExample(UpdateExampleReq req);

    /**
     * 删除example
     */
    void deleteExample(Long id);

    /**
     * 根据ID获取example
     */
    WebSandboxExampleDO getExampleById(Long id);

    /**
     * 获取所有library和charts的映射关系
     */
    Map<String, List<String>> getLibraryChartsMap();
}
