package com.pugwoo.branch.chart.service.impl;

import com.pugwoo.branch.chart.dto.valuedto.DateSingleDimValueDTO;
import com.pugwoo.branch.chart.dto.HtmlCssJsDTO;
import com.pugwoo.branch.chart.dto.highchart.CreatePercentageAreaDTO;
import com.pugwoo.branch.chart.service.HighchartCreateService;
import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.branch.common.VelocityTools;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class HighchartCreateServiceImpl implements HighchartCreateService {

    @Override
    public HtmlCssJsDTO createPercentageAreaChart(CreatePercentageAreaDTO dto) {
        if (dto == null) {
            return null;
        }

        HtmlCssJsDTO htmlCssJsDTO = getCommonHtmlCssJsDTO();
        Map<String, Object> params = new HashMap<>();
        params.put("TITLE", dto.getTitle());

        // 1. 找出所有的dim，保持按提供的数据的顺序
        List<String> dimList = DateSingleDimValueDTO.getDims(dto.getData());

        // 2. 构造所有的日期，只能排序
        List<String> datesList = DateSingleDimValueDTO.getDatesSortAsc(dto.getData());
        Map<String, Integer> datesIndex = new HashMap<>();
        params.put("DATE_ARRAY", ChartCommonUtils.makeArray(datesList, datesIndex));

        // 3. 构造数据
        Map<String, List<DateSingleDimValueDTO>> dataMapList = ListUtils.toMapList(dto.getData(),
                DateSingleDimValueDTO::getDim, o -> o);
        List<Map<String, Object>> series = new ArrayList<>();
        for (String dim : dimList) {
            Map<String, Object> seriesMap = new HashMap<>();
            seriesMap.put("name", dim);
            List<DateSingleDimValueDTO> d = dataMapList.get(dim);
            Map<String, BigDecimal> dataMap = ListUtils.toMap(d, DateSingleDimValueDTO::getDate, DateSingleDimValueDTO::getValue);
            List<BigDecimal> dataList = new ArrayList<>();
            for (String date : datesList) {
                dataList.add(dataMap.get(date));
            }
            seriesMap.put("data", ChartCommonUtils.makeArray(dataList));
            series.add(seriesMap);
        }
        params.put("SERIES", series);

        String js = VelocityTools.renderVM("/chart/highcharts/percentage_area.vm.js", params);
        htmlCssJsDTO.setJs(js);
        return htmlCssJsDTO;
    }


    private HtmlCssJsDTO getCommonHtmlCssJsDTO() {
        HtmlCssJsDTO htmlCssJsDTO = new HtmlCssJsDTO();
        String html = VelocityTools.renderVM("/chart/highcharts/common.vm.html", null);
        htmlCssJsDTO.setHtml(html);
        String css = VelocityTools.renderVM("/chart/highcharts/common.vm.css", null);
        htmlCssJsDTO.setCss(css);
        return htmlCssJsDTO;
    }

}
