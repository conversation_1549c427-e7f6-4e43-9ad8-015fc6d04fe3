package com.pugwoo.branch.chart.service.impl;

import com.pugwoo.branch.chart.dto.valuedto.DateSingleDimValueDTO;
import com.pugwoo.branch.chart.dto.HtmlCssJsDTO;
import com.pugwoo.branch.chart.dto.valuedto.SingleDimValueDTO;
import com.pugwoo.branch.chart.dto.echarts.Create3DBarDTO;
import com.pugwoo.branch.chart.dto.echarts.CreateBubbleDTO;
import com.pugwoo.branch.chart.dto.echarts.CreateLineDTO;
import com.pugwoo.branch.chart.dto.echarts.CreatePieDTO;
import com.pugwoo.branch.chart.dto.valuedto.XYDimValueDTO;
import com.pugwoo.branch.chart.service.EchartsCreateService;
import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.branch.common.VelocityTools;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class EchartsCreateServiceImpl implements EchartsCreateService {

    @Override
    public HtmlCssJsDTO createLine(CreateLineDTO dto) {
        if (dto == null) {
            return null;
        }

        HtmlCssJsDTO htmlCssJsDTO = getCommonHtmlCssJsDTO();
        Map<String, Object> params = new HashMap<>();
        params.put("TITLE", dto.getTitle());

        params.put("showZoom", dto.getShowZoom());
        params.put("xAxisName", dto.getXAxisName() == null ? "" : dto.getXAxisName());
        params.put("yAxisName", dto.getYAxisName() == null ? "" : dto.getYAxisName());

        // 1. 找出所有的dim，保持按提供的数据的顺序
        List<String> dimList = DateSingleDimValueDTO.getDims(dto.getData());
        params.put("DIM_ARRAY", ChartCommonUtils.makeArray(dimList));

        // 2. 判断下x轴是数值还是日期，如果是日期，则构造所有的日期，智能排序
        boolean isDatesAsNumber = DateSingleDimValueDTO.isDatesAsNumber(dto.getData());
        params.put("isDatesAsNumber", isDatesAsNumber);
        List<String> datesList = new ArrayList<>(); // 用于x轴是日期或字符串的场景，如果是日期则自动排序，如果是字符串则按原顺序
        if (!isDatesAsNumber) {
            datesList = DateSingleDimValueDTO.getDatesSortAsc(dto.getData());
            Map<String, Integer> datesIndex = new HashMap<>();
            params.put("DATE_ARRAY", ChartCommonUtils.makeArray(datesList, datesIndex));
        }

        // 多y轴
        params.put("isMultiYAxis", ListUtils.isNotEmpty(dto.getYAxis()));
        List<String> leftYAxis = new ArrayList<>();
        List<String> rightYAxis = new ArrayList<>();
        if (ListUtils.isNotEmpty(dto.getYAxis())) {
            leftYAxis.add(dto.getYAxis().getFirst().getName());
            for (int i = 1; i < dto.getYAxis().size(); i++) {
                rightYAxis.add(dto.getYAxis().get(i).getName());
            }
        }
        params.put("leftYAxis", leftYAxis);
        AtomicInteger offset = new AtomicInteger(0);
        params.put("rightYAxis", ListUtils.transform(rightYAxis, o -> {
            Map<String, Object> map = new HashMap<>();
            map.put("name", o);
            map.put("offset", offset.getAndIncrement() * 60);
            return map;
        }));

        // 3. 构造数据
        Map<String, List<DateSingleDimValueDTO>> dataMapList = ListUtils.toMapList(dto.getData(),
                DateSingleDimValueDTO::getDim, o -> o);
        List<Map<String, Object>> series = new ArrayList<>();
        for (String dim : dimList) {
            Map<String, Object> seriesMap = new HashMap<>();
            series.add(seriesMap);
            seriesMap.put("name", dim);
            List<DateSingleDimValueDTO> d = dataMapList.get(dim);

            if (isDatesAsNumber) {
                List<List<BigDecimal>> dataList = new ArrayList<>();
                for (DateSingleDimValueDTO dateSingleDimValueDTO : d) {
                    List<BigDecimal> list = new ArrayList<>();
                    list.add(NumberUtils.parseBigDecimal(dateSingleDimValueDTO.getDate()));
                    list.add(dateSingleDimValueDTO.getValue());
                    dataList.add(list);
                }
                seriesMap.put("data", ChartCommonUtils.makeArray(dataList));
            } else {
                Map<String, BigDecimal> dataMap = ListUtils.toMap(d, DateSingleDimValueDTO::getDate, DateSingleDimValueDTO::getValue);
                List<BigDecimal> dataList = new ArrayList<>();
                for (String date : datesList) {
                    dataList.add(dataMap.get(date));
                }
                seriesMap.put("data", ChartCommonUtils.makeArray(dataList));
            }

            if (dto.getColors() != null) {
                String color = ChartCommonUtils.getWildcardMatch(dto.getColors(), dim);
                if (StringTools.isNotBlank(color)) {
                    seriesMap.put("color", color);
                }
            }
            if (dto.getStyles() != null) {
                String style = ChartCommonUtils.getWildcardMatch(dto.getStyles(), dim);
                if (StringTools.isNotBlank(style)) {
                    seriesMap.put("style", style);
                }
            }

            seriesMap.put("smooth", dto.getSmooth());
            seriesMap.put("connectNulls", dto.getConnectNulls());

            // 查找数据对应的y轴
            if (ListUtils.isNotEmpty(dto.getYAxis())) {
                seriesMap.put("yAxisIndex", 0); // default
                for (int i = 0; i < dto.getYAxis().size(); i++) {
                    if (dto.getYAxis().get(i).getLines().contains(dim)) {
                        seriesMap.put("yAxisIndex", i);
                        break;
                    }
                }
            }
        }
        params.put("SERIES", series);

        String js = VelocityTools.renderVM("/chart/echarts/lines.vm.js", params);
        htmlCssJsDTO.setJs(js);
        return htmlCssJsDTO;
    }

    @Override
    public HtmlCssJsDTO createPie(CreatePieDTO dto) {
        if (dto == null) {
            return null;
        }

        HtmlCssJsDTO htmlCssJsDTO = getCommonHtmlCssJsDTO();
        Map<String, Object> params = new HashMap<>();
        params.put("TITLE", dto.getTitle());

        // 处理data
        List<Map<String, Object>> series = new ArrayList<>();
        for (SingleDimValueDTO d : dto.getData()) {
            Map<String, Object> seriesMap = new HashMap<>();
            seriesMap.put("name", d.getDim());
            seriesMap.put("value", d.getValue());
            series.add(seriesMap);
        }

        params.put("SERIES", series);

        // 处理配置
        params.put("showLegend", dto.getShowLegend());

        String label = "{b}";
        if (dto.getShowValue()) {
            label += ": {c}";
        }
        if (dto.getShowPercent()) {
            label += " ({d}%)";
        }
        params.put("label", label);

        String js = VelocityTools.renderVM("/chart/echarts/pie.vm.js", params);
        htmlCssJsDTO.setJs(js);

        return htmlCssJsDTO;
    }

    @Override
    public HtmlCssJsDTO createBubble(CreateBubbleDTO dto) {
        if (dto == null) {
            return null;
        }
        HtmlCssJsDTO htmlCssJsDTO = getCommonHtmlCssJsDTO();
        Map<String, Object> params = new HashMap<>();
        params.put("TITLE", dto.getTitle());

        List<String> dims = XYDimValueDTO.getDims(dto.getData());
        params.put("legend", ChartCommonUtils.makeArray(dims));

        params.put("xMin", dto.getXMin());
        params.put("xMax", dto.getXMax());
        params.put("yMin", dto.getYMin());
        params.put("yMax", dto.getYMax());

        List<Map<String, Object>> series = new ArrayList<>();
        for (String dim : dims) {
            Map<String, Object> seriesMap = new HashMap<>();
            seriesMap.put("name", dim);

            List<List<Object>> data = new ArrayList<>();
            for (XYDimValueDTO d : dto.getData()) {
                if (Objects.equals(dim, d.getDim())) {
                    List<Object> dataItem = new ArrayList<>();
                    dataItem.add(d.getXAxis());
                    dataItem.add(d.getYAxis());
                    dataItem.add(d.getValue());
                    dataItem.add(d.getName());
                    dataItem.add(d.getDim());
                    data.add(dataItem);
                }
            }

            seriesMap.put("data", ChartCommonUtils.makeArray(data));
            seriesMap.put("pointZoom", dto.getPointZoom());
            series.add(seriesMap);
        }

        params.put("SERIES", series);

        String js = VelocityTools.renderVM("/chart/echarts/bubble.vm.js", params);
        htmlCssJsDTO.setJs(js);

        return htmlCssJsDTO;
    }

    @Override
    public HtmlCssJsDTO create3DBar(Create3DBarDTO dto) {
        if (dto == null) {
            return null;
        }
        HtmlCssJsDTO htmlCssJsDTO = getCommonHtmlCssJsDTO();
        Map<String, Object> params = new HashMap<>();
        params.put("TITLE", dto.getTitle());

        // 1. 处理date，生成其对应的index
        Set<String> datesSet = ListUtils.toSet(dto.getData(), DateSingleDimValueDTO::getDate);
        List<String> datesList = ListUtils.toList(datesSet);
        ChartCommonUtils.smartSortByDate(datesList);
        Map<String, Integer> datesIndex = new HashMap<>();
        params.put("DATE_ARRAY", ChartCommonUtils.makeArray(datesList, datesIndex));

        // 2. 处理y轴，生成对应的index，y轴实际顺序是啥就是啥，不排序
        List<String> dimsList = new ArrayList<>();
        Set<String> existDims = new HashSet<>();
        for (DateSingleDimValueDTO d : dto.getData()) {
            if (!existDims.contains(d.getDim())) {
                dimsList.add(d.getDim());
                existDims.add(d.getDim());
            }
        }
        Map<String, Integer> dimsIndex = new HashMap<>();
        params.put("DIM_ARRAY", ChartCommonUtils.makeArray(dimsList, dimsIndex));

        // 3. 处理data
        BigDecimal maxValue = null;
        StringBuilder dataArraySb = new StringBuilder("[");
        for (DateSingleDimValueDTO d : dto.getData()) {
            dataArraySb.append("[").append(dimsIndex.get(d.getDim())).append(",")
                    .append(datesIndex.get(d.getDate())).append(",").append(d.getValue()).append("],");
            if (maxValue == null || d.getValue().compareTo(maxValue) > 0) {
                maxValue = d.getValue();
            }
        }
        dataArraySb.append("]");
        params.put("DATA_ARRAY", dataArraySb.toString());

        if (maxValue == null) {
            maxValue = BigDecimal.ZERO;
        }
        params.put("MAX_VALUE", dto.getMaxValue() != null ? dto.getMaxValue() : maxValue);

        // 4. 其他
        params.put("BOX_WIDTH", dto.getBoxWidth());
        params.put("BOX_DEPTH", dto.getBoxDepth());
        params.put("BAR_SIZE", dto.getBarSize());
        params.put("DISTANCE", dto.getDistance());
        params.put("MAX_DISTANCE", Math.max(dto.getMaxDistance(), dto.getDistance()));
        params.put("HEIGHT_ANGLE", dto.getHeightAngle());

        String js = VelocityTools.renderVM("/chart/echarts/3d_bar.vm.js", params);
        htmlCssJsDTO.setJs(js);

        return htmlCssJsDTO;
    }

    private HtmlCssJsDTO getCommonHtmlCssJsDTO() {
        HtmlCssJsDTO htmlCssJsDTO = new HtmlCssJsDTO();
        String html = VelocityTools.renderVM("/chart/echarts/common.vm.html", null);
        htmlCssJsDTO.setHtml(html);
        String css = VelocityTools.renderVM("/chart/echarts/common.vm.css", null);
        htmlCssJsDTO.setCss(css);
        return htmlCssJsDTO;
    }

}
