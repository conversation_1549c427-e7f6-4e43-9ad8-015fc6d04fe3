package com.pugwoo.branch.chart.web.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CreateSandboxReq {

    /**是否是临时图表，当是临时图标时，它只会在redis中存留10分钟*/
    private Boolean tmp;

    /**图表的名称，建议提供*/
    private String name;

    /**组件库，例如echarts*/
    private String library;

    /**组件名称，例如3Dbar*/
    @JsonAlias({ "charts", "chart" })
    private String charts;

    /**表头，弱化表头校验，以后不叫titles，换成headers*/
    @JsonAlias({ "headers", "header", "titles", "title" })
    private List<String> headers;

    /**数据*/
    private List<List<String>> data;

    /**配置项*/
    @JsonAlias({ "configs", "config" })
    private Map<String, Object> configs;

    /**原始数据，用于PlantUML等需要原始文本的图表类型*/
    private String rawData;

}
