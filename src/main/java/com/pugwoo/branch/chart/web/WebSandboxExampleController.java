package com.pugwoo.branch.chart.web;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.NotRequireLogin;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.chart.entity.WebSandboxExampleDO;
import com.pugwoo.branch.chart.service.WebSandboxExampleInitService;
import com.pugwoo.branch.chart.service.WebSandboxExampleService;
import com.pugwoo.branch.chart.web.req.CreateExampleReq;
import com.pugwoo.branch.chart.web.req.GenerateLinkCodeReq;
import com.pugwoo.branch.chart.web.req.UpdateExampleReq;
import com.pugwoo.branch.chart.web.resp.CreateSandboxResp;
import com.pugwoo.branch.chart.web.resp.LibraryChartsResp;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

@NotRequireLogin
@RestController
@RequestMapping("/chart_examples")
public class WebSandboxExampleController {

    @Resource
    private WebSandboxExampleService webSandboxExampleService;

    @Resource
    private WebSandboxExampleInitService webSandboxExampleInitService;

    /**
     * 图表示例管理页面
     */
    @GetMapping("")
    public ModelAndView chartExamplesPage() {
        return new ModelAndView("chart/chart_examples");
    }

    /**
     * 获取所有library和charts的映射关系
     */
    @GetMapping("/library_charts")
    public LibraryChartsResp getLibraryCharts() {
        LibraryChartsResp resp = new LibraryChartsResp();
        resp.setLibraryChartsMap(webSandboxExampleService.getLibraryChartsMap());
        resp.setLibraries(webSandboxExampleService.getAllLibraries());
        return resp;
    }

    /**
     * 根据library和charts获取examples
     */
    @GetMapping("/examples")
    public List<WebSandboxExampleDO> getExamples(@RequestParam String library, @RequestParam String charts) {
        WebCheckUtils.assertNotBlank(library, "library不能为空");
        WebCheckUtils.assertNotBlank(charts, "charts不能为空");
        return webSandboxExampleService.getExamplesByLibraryAndCharts(library, charts);
    }

    /**
     * 生成linkCode（临时或永久）
     */
    @PostMapping("/generate_link_code")
    public CreateSandboxResp generateLinkCode(@RequestBody GenerateLinkCodeReq req) {
        WebCheckUtils.assertNotNull(req, "缺少参数");
        WebCheckUtils.assertNotBlank(req.getReqBody(), "请求体不能为空");
        
        boolean temporary = req.getTemporary() != null && req.getTemporary();
        String linkCode = webSandboxExampleService.getOrCreateLinkCode(req.getReqBody(), temporary);
        
        CreateSandboxResp resp = new CreateSandboxResp();
        resp.setLinkCode(linkCode);
        return resp;
    }

    /**
     * 创建新的example
     */
    @PostMapping("/create")
    public WebJsonBean<?> createExample(@RequestBody CreateExampleReq req) {
        WebCheckUtils.assertNotNull(req, "缺少参数");
        WebSandboxExampleDO example = webSandboxExampleService.createExample(req);
        return example != null && example.getId() != null ? WebJsonBean.ok() : WebJsonBean.fail("创建失败");
    }

    /**
     * 更新example
     */
    @PostMapping("/update")
    public WebSandboxExampleDO updateExample(@RequestBody UpdateExampleReq req) {
        WebCheckUtils.assertNotNull(req, "缺少参数");
        return webSandboxExampleService.updateExample(req);
    }

    /**
     * 删除example
     */
    @PostMapping("/delete")
    public void deleteExample(@RequestParam Long id) {
        WebCheckUtils.assertNotNull(id, "ID不能为空");
        webSandboxExampleService.deleteExample(id);
    }

    /**
     * 根据ID获取example
     */
    @GetMapping("/get")
    public WebSandboxExampleDO getExample(@RequestParam Long id) {
        WebCheckUtils.assertNotNull(id, "ID不能为空");
        return webSandboxExampleService.getExampleById(id);
    }

    /**
     * 初始化示例数据（管理功能）
     */
    @PostMapping("/init_data")
    public String initExampleData() {
        webSandboxExampleInitService.initExampleData();
        return "示例数据初始化完成";
    }
}
