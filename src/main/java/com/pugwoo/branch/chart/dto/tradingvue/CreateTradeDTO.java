package com.pugwoo.branch.chart.dto.tradingvue;

import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import lombok.SneakyThrows;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class CreateTradeDTO {

    /**
     * 图表标题
     */
    private String title;

    /**
     * OHLCV数据：[时间戳, 开盘价, 最高价, 最低价, 收盘价, 成交量]
     */
    private List<List<Object>> ohlcvData = new ArrayList<>();

    /**
     * 主图指标数据
     */
    private List<Map<String, Object>> onchartData = new ArrayList<>();

    /**
     * 副图指标数据
     */
    private List<Map<String, Object>> offchartData = new ArrayList<>();

    public static CreateTradeDTO from(CreateSandboxReq req) {
        CreateTradeDTO dto = new CreateTradeDTO();
        dto.setTitle(req.getName() != null ? req.getName() : "K线图");

        if (req.getData() != null && !req.getData().isEmpty()) {
            List<List<Object>> ohlcvData = new ArrayList<>();

            for (List<String> row : req.getData()) {
                if (row.size() >= 6) {
                    List<Object> ohlcvRow = new ArrayList<>();

                    // 转换日期格式：从20250508转换为时间戳
                    String dateStr = row.get(0);
                    long timestamp = convertDateToTimestamp(dateStr);
                    ohlcvRow.add(timestamp);

                    // 添加开盘价、最高价、最低价、收盘价、成交量
                    for (int i = 1; i < 6; i++) {
                        BigDecimal value = NumberUtils.parseBigDecimal(row.get(i));
                        ohlcvRow.add(value != null ? value : BigDecimal.ZERO);
                    }

                    ohlcvData.add(ohlcvRow);
                }
            }

            // ohlcvData时间要正序排列
            ListUtils.sortAscNullLast(ohlcvData, o -> (long) o.getFirst());

            dto.setOhlcvData(ohlcvData);
        }

        // 处理 configs 中的 onchart 和 offchart 配置
        if (req.getConfigs() != null) {
            // 处理 onchart 配置
            Object onchartConfig = req.getConfigs().get("onchart");
            if (onchartConfig instanceof List<?>) {
                dto.setOnchartData(processChartConfig((List<?>) onchartConfig));
            }

            // 处理 offchart 配置
            Object offchartConfig = req.getConfigs().get("offchart");
            if (offchartConfig instanceof List<?>) {
                dto.setOffchartData(processChartConfig((List<?>) offchartConfig));
            }
        }

        return dto;
    }

    /**
     * 处理图表配置数据，将日期转换为时间戳
     * @param configList 配置列表
     * @return 处理后的配置数据
     */
    @SuppressWarnings("unchecked")
    private static List<Map<String, Object>> processChartConfig(List<?> configList) {
        List<Map<String, Object>> result = new ArrayList<>();

        for (Object configObj : configList) {
            if (configObj instanceof Map<?, ?>) {
                Map<String, Object> config = (Map<String, Object>) configObj;
                Map<String, Object> processedConfig = new HashMap<>();

                // 复制基本属性
                processedConfig.put("name", config.get("name"));
                processedConfig.put("type", config.get("type"));

                // 处理 data 数组，将日期转换为时间戳
                Object dataObj = config.get("data");
                if (dataObj instanceof List<?>) {
                    List<?> dataList = (List<?>) dataObj;
                    List<List<Object>> processedData = new ArrayList<>();

                    for (Object dataPoint : dataList) {
                        if (dataPoint instanceof List<?>) {
                            List<?> point = (List<?>) dataPoint;
                            if (point.size() >= 2) {
                                List<Object> processedPoint = new ArrayList<>();

                                // 转换第一个元素（日期）为时间戳
                                Object dateObj = point.get(0);
                                if (dateObj instanceof Number) {
                                    // 如果是数字格式的日期（如20250508）
                                    long timestamp = convertDateToTimestamp(dateObj.toString());
                                    processedPoint.add(timestamp);
                                } else if (dateObj instanceof String) {
                                    // 如果是字符串格式的日期
                                    long timestamp = convertDateToTimestamp((String) dateObj);
                                    processedPoint.add(timestamp);
                                } else {
                                    processedPoint.add(dateObj);
                                }

                                // 添加其他数值
                                for (int i = 1; i < point.size(); i++) {
                                    Object value = point.get(i);
                                    if (value instanceof String) {
                                        BigDecimal numValue = NumberUtils.parseBigDecimal((String) value);
                                        processedPoint.add(numValue != null ? numValue : BigDecimal.ZERO);
                                    } else {
                                        processedPoint.add(value);
                                    }
                                }

                                processedData.add(processedPoint);
                            }
                        }
                    }

                    // 按时间戳排序
                    ListUtils.sortAscNullLast(processedData, o -> (long) o.get(0));
                    processedConfig.put("data", processedData);
                }

                // 复制 settings 配置
                if (config.containsKey("settings")) {
                    processedConfig.put("settings", config.get("settings"));
                }

                result.add(processedConfig);
            }
        }

        return result;
    }

    /**
     * 将日期字符串转换为时间戳(毫秒)
     * @param dateStr 格式如：20250508
     * @return 时间戳（毫秒）
     */
    @SneakyThrows
    private static long convertDateToTimestamp(String dateStr) {
        LocalDate date = DateUtils.parseLocalDateThrowException(dateStr);
        return date.atStartOfDay(ZoneOffset.UTC).toInstant().toEpochMilli();
    }
}
