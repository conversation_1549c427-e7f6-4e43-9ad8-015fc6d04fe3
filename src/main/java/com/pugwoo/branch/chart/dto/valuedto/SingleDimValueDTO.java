package com.pugwoo.branch.chart.dto.valuedto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 单一维度+数值的DTO，适用于饼图
 */
@Data
public class SingleDimValueDTO {

    /**维度值，必须*/
    private String dim;

    /**数值，必须*/
    private BigDecimal value;

    /**
     * 找出所有的dim，保持按提供的数据的顺序
     */
    public static List<String> getDims(List<SingleDimValueDTO> data) {
        if (data == null) {
            return new ArrayList<>();
        }
        List<String> dimList = new ArrayList<>();
        Set<String> dimSet = new HashSet<>();
        for (SingleDimValueDTO d : data) {
            if (!dimSet.contains(d.getDim())) {
                dimList.add(d.getDim());
                dimSet.add(d.getDim());
            }
        }
        return dimList;
    }
}
