package com.pugwoo.branch.chart.dto.plantuml;

import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import lombok.Data;

@Data
public class CreatePlantUmlDTO {

    /**
     * 图表标题
     */
    private String title;

    /**
     * PlantUML原始代码
     */
    private String umlCode;

    public static CreatePlantUmlDTO from(CreateSandboxReq req) {
        CreatePlantUmlDTO dto = new CreatePlantUmlDTO();
        dto.setTitle(req.getName() != null ? req.getName() : "");
        dto.setUmlCode(req.getRawData());
        return dto;
    }

}
