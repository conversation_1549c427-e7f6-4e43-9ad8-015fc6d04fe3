package com.pugwoo.branch.chart.dto.valuedto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 适合于气泡图的数据，有x轴和y轴、维度、维度值、数值
 */
@Data
public class XYDimValueDTO {

    /**x轴*/
    private BigDecimal xAxis;

    /**y轴*/
    private BigDecimal yAxis;

    /**维度*/
    private String dim;

    /**维度名称*/
    private String name;

    /**数值，该数值决定气泡大小*/
    private BigDecimal value;

    /**
     * 找出所有的dim，保持按提供的数据的顺序
     */
    public static List<String> getDims(List<XYDimValueDTO> data) {
        if (data == null) {
            return new ArrayList<>();
        }
        List<String> dimList = new ArrayList<>();
        Set<String> dimSet = new HashSet<>();
        for (XYDimValueDTO d : data) {
            if (!dimSet.contains(d.getDim())) {
                dimList.add(d.getDim());
                dimSet.add(d.getDim());
            }
        }
        return dimList;
    }
}
