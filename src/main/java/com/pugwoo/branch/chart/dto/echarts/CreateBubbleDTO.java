package com.pugwoo.branch.chart.dto.echarts;

import com.pugwoo.branch.chart.dto.valuedto.XYDimValueDTO;
import com.pugwoo.branch.chart.utils.ChartCommonUtils;
import com.pugwoo.branch.chart.web.req.CreateSandboxReq;
import com.pugwoo.wooutils.lang.NumberUtils;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class CreateBubbleDTO {

    @NotNull
    private List<XYDimValueDTO> data = new ArrayList<>();
    @NotNull
    private String title = "";

    /**圆点的缩放，值越大，点越大*/
    private BigDecimal pointZoom = BigDecimal.valueOf(100);

    // 下面这4个控制x轴和y轴的范围，为null表示不设置

    private BigDecimal xMin = null;
    private BigDecimal xMax = null;
    private BigDecimal yMin = null;
    private BigDecimal yMax = null;

    public static CreateBubbleDTO from(CreateSandboxReq req) {
        CreateBubbleDTO createBubbleDTO = new CreateBubbleDTO();
        createBubbleDTO.setTitle(req.getName() == null ? "" : req.getName());

        Integer columns = ChartCommonUtils.determinateColumn(req.getData());
        if (columns != null && columns != 5) {
            throw new RuntimeException("表列数只支持5列，实际列数：" + columns);
        }

        if (req.getData() != null) {
            for (List<String> row : req.getData()) {
                XYDimValueDTO d = new XYDimValueDTO();
                d.setXAxis(NumberUtils.parseBigDecimal(row.get(0)));
                d.setYAxis(NumberUtils.parseBigDecimal(row.get(1)));
                d.setDim(row.get(2));
                d.setName(row.get(3));
                d.setValue(NumberUtils.parseBigDecimal(row.get(4)));
                createBubbleDTO.getData().add(d);
            }
        }

        // 配置信息
        createBubbleDTO.setPointZoom(ChartCommonUtils.getConfig(req.getConfigs(), "pointZoom", createBubbleDTO.getPointZoom(),
                NumberUtils::parseBigDecimal));
        createBubbleDTO.setXMin(ChartCommonUtils.getConfig(req.getConfigs(), "xMin", createBubbleDTO.getXMin(),
                NumberUtils::parseBigDecimal));
        createBubbleDTO.setXMax(ChartCommonUtils.getConfig(req.getConfigs(), "xMax", createBubbleDTO.getXMax(),
                NumberUtils::parseBigDecimal));
        createBubbleDTO.setYMin(ChartCommonUtils.getConfig(req.getConfigs(), "yMin", createBubbleDTO.getYMin(),
                NumberUtils::parseBigDecimal));
        createBubbleDTO.setYMax(ChartCommonUtils.getConfig(req.getConfigs(), "yMax", createBubbleDTO.getYMax(),
                NumberUtils::parseBigDecimal));

        return createBubbleDTO;
    }

}
