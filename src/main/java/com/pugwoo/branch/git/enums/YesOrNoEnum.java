package com.pugwoo.branch.git.enums;

public enum YesOrNoEnum {
    YES("YES", "是"),
    NO("NO", "否");

    private String code;

    private String name;

    YesOrNoEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static YesOrNoEnum getByCode(String code) {
        for (YesOrNoEnum e : YesOrNoEnum.values()) {
            if (code == e.getCode() || code != null && code.equals(e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        YesOrNoEnum statusEnum = getByCode(code);
        if (statusEnum != null) {
            return statusEnum.getName();
        }
        return "";
    }
}