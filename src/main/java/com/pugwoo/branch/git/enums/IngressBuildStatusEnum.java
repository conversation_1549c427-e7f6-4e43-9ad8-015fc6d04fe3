package com.pugwoo.branch.git.enums;

public enum IngressBuildStatusEnum {

    NEW("NEW", "before query build status"),
    RUNNING("RUNNING", "build running"),
    SUCCESS("SUCCESS", "build success"),
    FAILURE("FAILURE","build failure"),
    QUERY_TIMEOUT("QUERY_TIMEOUT", "query build status timeout"), // 默认超过24小时就不再查询
            ;

    private String code;

    private String name;

    IngressBuildStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static IngressBuildStatusEnum getByCode(String code) {
        for (IngressBuildStatusEnum e : IngressBuildStatusEnum.values()) {
            if (code == e.getCode() || code != null && code.equals(e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        IngressBuildStatusEnum e = getByCode(code);
        if (e != null) {
            return e.getName();
        }
        return "";
    }
}
