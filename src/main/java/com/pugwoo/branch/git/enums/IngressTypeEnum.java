package com.pugwoo.branch.git.enums;

public enum IngressTypeEnum {

    BRANCH_NEW("BRANCH_NEW", "创建新分支"),
    BRANCH_DEL("BRANCH_DEL", "删除分支"),
    BRANCH_COPY("BRANCH_COPY", "复制分支"),
    BRANCH_RESET("BRANCH_RESET", "回退分支"),

    DEVELOP("DEVELOP", "开发环境"),
    DEVELOP_API("DEVELOP_API", "开发环境发布API"),

    TEST("TEST", "测试环境"),
    TEST_LOCK("TEST_LOCK", "锁定版本"),
    TEST_UNLOCK("TEST_UNLOCK", "解锁版本"),
    TEST_TAG_DEL("TEST_TAG_DEL", "删除版本"),

    PROD("PROD", "生产环境"),
    PROD_API("PROD_API", "生产环境发布API")
    ;

    private String code;

    private String name;

    IngressTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static IngressTypeEnum getByCode(String code) {
        for (IngressTypeEnum e : IngressTypeEnum.values()) {
            if (code == e.getCode() || code != null && code.equals(e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        IngressTypeEnum statusEnum = getByCode(code);
        if (statusEnum != null) {
            return statusEnum.getName();
        }
        return "";
    }
}
