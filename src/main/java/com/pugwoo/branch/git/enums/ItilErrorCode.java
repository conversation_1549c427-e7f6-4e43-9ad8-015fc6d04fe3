package com.pugwoo.branch.git.enums;

import com.pugwoo.admin.bean.ErrorCode;
import lombok.Getter;

@Getter
public enum ItilErrorCode implements ErrorCode {

    SYSTEM_ERROR(-1, "系统未知错误"),
    MERGING_ERROR(10, "合并分支出现冲突"),
    BRANCH_ERROR(11, "此分支已存在"),
    NEVER_DEVELOP(12, "还未发布版本，无法对比"),
    NO_SUCH_TAG(13, "还没有此tag，无法比对"),
    PULL_ERROR(14, "拉取代码失败"),
    NOT_ALLOW_OP_MASTER(15, "不允许操作master分支"),
    BRANCH_DELETE(16, "旧分支已被删除"),
    DELETE_REMOTE_BRANCH_FAIL(17, "删除远程分支失败"),
    CREATE_DIR_FAIL(18, "创建本地Git目录失败"),
    LOCATION_IS_NOT_DIR(19, "仓库目录不存在或不是文件夹"),
    PUSH_ERROR(20, "推送代码到远程失败"),
    MERGE_NOT_WORK(21, "合并代码发现commit id不一致，请确认master分支没有被修改过，或者勾选【强制合并】"),
    TAG_ALREADY_MERGED(22, "要发布的tag已经合并到master了，请使用新的tag"),
    CHECK_RULE_CREATE_ERROR(23, "创建校验规则失败"),
    GIT_NOT_INIT_YET(24, "仓库未初始化，请先初始化仓库"),
    BRANCH_NOT_EXIST(25, "分支不存在")
    ;
    private int code;
    private String name;

    ItilErrorCode(int code, String name) {
        this.code = code;
        this.name = name;
    }

}