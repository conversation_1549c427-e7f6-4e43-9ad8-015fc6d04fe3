package com.pugwoo.branch.git.vo;

import com.pugwoo.branch.git.entity.GitIngressBranchDO;
import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.util.List;

@Data
public class GitIngressVO extends GitIngressDO {

    @RelatedColumn(localColumn = "id", remoteColumn = "ingress_id")
    private List<GitIngressBranchDO> gitIngressBranchDOList;

    @RelatedColumn(localColumn = "create_user_id", remoteColumn = "id")
    private AdminUserDO adminUserDO;

    /**获取创建者名称*/
    public String getCreateUserName() {
        if (adminUserDO == null) {
            return "";
        }

        return StringTools.isEmpty(adminUserDO.getRealName()) ?
                adminUserDO.getUserName() : adminUserDO.getRealName();
    }

}