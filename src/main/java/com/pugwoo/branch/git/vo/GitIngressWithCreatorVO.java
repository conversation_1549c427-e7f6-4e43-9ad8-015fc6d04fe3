package com.pugwoo.branch.git.vo;

import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.wooutils.string.StringTools;

public class GitIngressWithCreatorVO extends GitIngressDO {

    @RelatedColumn(localColumn = "create_user_id", remoteColumn = "id")
    private AdminUserDO adminUserDO;

    /**获取创建者名称*/
    public String getCreateUserName() {
        if (adminUserDO == null) {
            return "";
        }

        return StringTools.isEmpty(adminUserDO.getRealName()) ?
                adminUserDO.getUserName() : adminUserDO.getRealName();
    }

}
