package com.pugwoo.branch.git.vo;

import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.entity.GitTagsDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 为了获取编译状态的vo
 */
@Data
public class GitRepo4BuildStatusVO extends GitRepositoryDO {

    @Data
    public static class GitTags4BuildStatusVO extends GitTagsDO {

        @RelatedColumn(localColumn = "ingress_id", remoteColumn = "id")
        private GitIngressDO gitIngressDO;

        public String getBuildStatus() {
            return gitIngressDO == null ? "" : gitIngressDO.getBuildStatus();
        }

    }

    @RelatedColumn(localColumn = "id", remoteColumn = "repository_id", extraWhere = "where is_merge_to_master='NO'")
    private List<GitTags4BuildStatusVO> gitTags;

    @RelatedColumn(localColumn = "last_develop_ingress_id", remoteColumn = "id")
    private GitIngressDO lastDevelopIngressDO; // 最后部署的开发ingress

    public String getLastDevelopBuildStatus() {
        return lastDevelopIngressDO == null ? null : lastDevelopIngressDO.getBuildStatus();
    }

    public Map<String, String> getTagBuildStatus() {
        Map<String, String> map = new HashMap<>();
        if(gitTags != null) {
            for(GitTags4BuildStatusVO tag : gitTags) {
                map.put(tag.getTagName(), tag.getBuildStatus());
            }
        }
        return map;
    }

}
