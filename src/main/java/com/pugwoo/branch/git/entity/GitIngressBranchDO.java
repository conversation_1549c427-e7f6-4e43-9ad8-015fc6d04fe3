package com.pugwoo.branch.git.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("git_ingress_branch")
public class GitIngressBranchDO extends AdminBaseDO {

    /** 版本id<br>Column: [ingress_id] */
    @Column(value = "ingress_id")
    private Long ingressId;

    /** 分支名称<br>Column: [branch_name] */
    @Column(value = "branch_name")
    private String branchName;

    /** 提交commit id<br>Column: [commit_id] */
    @Column(value = "commit_id")
    private String commitId;

}