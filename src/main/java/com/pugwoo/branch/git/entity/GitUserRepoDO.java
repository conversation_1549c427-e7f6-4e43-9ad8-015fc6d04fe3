package com.pugwoo.branch.git.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("git_user_repo")
public class GitUserRepoDO extends AdminBaseDO {

    /** 用户id<br>Column: [user_id] */
    @Column(value = "user_id")
    private Long userId;

    /** 仓库url地址，靠这个判断权限<br>Column: [url] */
    @Column(value = "url")
    private String url;

}
