package com.pugwoo.branch.git.utils;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 版本相关的工具类
 */
public class VersionUtils {

    public static void sortVersionDesc(List<String> tags) {
        if(tags == null || tags.isEmpty()) {
            return;
        }
        ListUtils.sortDescNullLast(tags, o -> trans(o));
    }

    public static void sortVersionAsc(List<String> tags) {
        if(tags == null || tags.isEmpty()) {
            return;
        }
        ListUtils.sortAscNullLast(tags, o -> trans(o));
    }

    private static String trans(String version) {
        if(StringTools.isBlank(version)) {
            return version;
        }

        Pattern pattern = Pattern.compile("\\d+");
        Matcher m = pattern.matcher(version);

        int paddingLen = 20;

        StringBuilder sb = new StringBuilder();
        int lastStart = 0;
        while (m.find()) {
            int start = m.start();
            int end = m.end();
            String str = m.group();

            sb.append(version.substring(lastStart, start));
            for(int i = str.length(); i < paddingLen; i++) {
                sb.append("0");
            }
            sb.append(str);
            lastStart = end;
        }

        sb.append(version.substring(lastStart));

        return sb.toString();
    }

}
