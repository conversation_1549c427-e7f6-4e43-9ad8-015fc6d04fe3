package com.pugwoo.branch.git.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.maven.model.Model;
import org.apache.maven.model.io.xpp3.MavenXpp3Reader;

import java.io.ByteArrayInputStream;

/**
 * 操作pom.xml
 */
@Slf4j
public class PomUtils {

    /**
     * 获得pom.xml版本信息
     * @param pomXmlContent
     * @return 返回null失败
     */
    public static String getVersion(String pomXmlContent) {
        MavenXpp3Reader reader = new MavenXpp3Reader();
        try {
            // ByteArrayInputStream不需要close
            Model model = reader.read(new ByteArrayInputStream(pomXmlContent.getBytes()));
            return model.getVersion();
        } catch (Exception e) {
            log.error("parse pom file fail", e);
            return null;
        }
    }

}
