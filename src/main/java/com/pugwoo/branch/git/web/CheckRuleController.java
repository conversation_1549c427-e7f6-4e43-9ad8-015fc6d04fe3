package com.pugwoo.branch.git.web;

import com.pugwoo.branch.code_scan.enums.CheckRuleLevelEnum;
import com.pugwoo.branch.code_scan.enums.CheckRuleTypeEnum;
import com.pugwoo.branch.code_scan.model.GitCodeCheckRuleBO;
import com.pugwoo.branch.git.service.ICheckRuleService;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

@Permission(value = "CheckGit", name = "Git Check查看列表")
@Controller
@RequestMapping("/check_rule")
public class CheckRuleController {

    @Autowired
    private ICheckRuleService checkRuleService;

    @RequestMapping("list")
    public String list() {
        return "git/check_rule_list";
    }

    @ResponseBody
    @RequestMapping("get_page")
    public WebJsonBean getPage(String name, int page, int pageSize) {
        PageData<GitCodeCheckRuleBO> pageData = checkRuleService.getPage(name, page, pageSize);
        Map<String, Object> data = PageUtils.trans(pageData, o -> {
            Map<String, Object> m = new HashMap<>();
            m.put("name", o.getName());
            m.put("files", ListUtils.transform(o.getCheckFiles(),
                    t -> MapUtils.of("name", t.getName(),
                            "regex", t.getFileRegex())));

            m.put("rules", ListUtils.transform(o.getGitCodeCheckRuleDetailDOList(), t -> {
                Map<String, Object> map = new HashMap<>();
                map.put("type", CheckRuleTypeEnum.getNameByCode(t.getType()));
                map.put("content", t.getContentRegex());
                map.put("level", CheckRuleLevelEnum.getNameByCode(t.getLevel()));
                return map;
            }));
            return m;
        });
        return WebJsonBean.ok(data);
    }

}