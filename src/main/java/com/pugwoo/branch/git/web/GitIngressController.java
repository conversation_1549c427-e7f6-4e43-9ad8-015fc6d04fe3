package com.pugwoo.branch.git.web;

import com.pugwoo.branch.git.entity.GitIngressBranchDO;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.enums.IngressTypeEnum;
import com.pugwoo.branch.git.service.IGitIngressService;
import com.pugwoo.branch.git.service.IGitRepositoryService;
import com.pugwoo.branch.git.vo.GitIngressVO;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

@Permission(value = "ReadGit", name = "Git查看列表")
@Controller
@RequestMapping("/git_ingress")
public class GitIngressController {

    @Autowired
    private IGitIngressService gitIngressService;
    @Autowired
    private IGitRepositoryService repositoryService;

    @RequestMapping("list")
    public String list() {
        return "git/ingress_list";
    }

    /**
     * @param type 如有多个，则用逗号隔开
     */
    @ResponseBody
    @RequestMapping("get_page")
    public WebJsonBean getPage(Long repositoryId, String type, int page, int pageSize) {
        WebCheckUtils.assertNotNull(repositoryId, "缺少参数repositoryId");

        List<IngressTypeEnum> types = new ArrayList<>();
        if (StringTools.isNotBlank(type)) {
            String[] split = type.split(",");
            for (String s : split) {
                IngressTypeEnum t = IngressTypeEnum.getByCode(s);
                if (t != null) {
                    types.add(t);
                }
            }
        }
        PageData<GitIngressVO> pageData = gitIngressService.getPageWith(repositoryId, types, page, pageSize);

        List<Map<String, Object>> list = new ArrayList();
        List<GitIngressVO> data = pageData.getData();
        if (data != null && data.size() > 0) {
            for (int i = 0; i < data.size(); i++) {
                Map<String, Object> map = new HashMap();
                GitIngressVO o = data.get(i);
                map.put("id", o.getId());
                map.put("createTime", o.getCreateTime());
                map.put("type", IngressTypeEnum.getNameByCode(o.getType()));
                map.put("name", o.getName());
                map.put("commitId", o.getCommitId());
                map.put("commitIdShow", cut(o.getCommitId(), 10));
                map.put("createUser", o.getCreateUserName());

                GitIngressVO near = null; // 查找相同发布环境的最后一条记录
                // 特别的，只针对这些类型进行比较
                IngressTypeEnum typeE = IngressTypeEnum.getByCode(o.getType());
                if(typeE == IngressTypeEnum.DEVELOP || typeE == IngressTypeEnum.DEVELOP_API || typeE == IngressTypeEnum.TEST
                   || typeE == IngressTypeEnum.PROD || typeE == IngressTypeEnum.PROD_API) {
                    for (int j = i + 1; j < data.size(); j++) {
                        GitIngressVO gitIngressVO = data.get(j);
                        if (gitIngressVO.getType().equals(o.getType())) {
                            near = gitIngressVO;
                            break;
                        }
                    }
                }

                if(near != null) {
                    map.put("comparison", true);
                    map.put("otherCommitId", near.getCommitId());
                } else {
                    map.put("comparison", false);
                }

                List<Map<String, Object>> branchDOs = new ArrayList<>();
                for (int j = 0; j < o.getGitIngressBranchDOList().size(); j++) {
                    GitIngressBranchDO gitIngressBranchDO = o.getGitIngressBranchDOList().get(j);
                    Map<String, Object> m = new HashMap<>();
                    m.put("id", gitIngressBranchDO.getId());
                    m.put("branchName", gitIngressBranchDO.getBranchName());
                    m.put("commitIdShow", cut(gitIngressBranchDO.getCommitId(), 10));
                    m.put("commitId", gitIngressBranchDO.getCommitId());

                    if (near != null) {
                        List<GitIngressBranchDO> lastIngressBranchDOList = near.getGitIngressBranchDOList();
                        Optional<GitIngressBranchDO> first = lastIngressBranchDOList.stream().filter(t -> t.getBranchName().equals(gitIngressBranchDO.getBranchName())).findFirst();
                        //分支进行对比，如果分支不相同，则进行对比
                        if (first.isPresent()) {
                            if (!Objects.equals(gitIngressBranchDO.getCommitId(), first.get().getCommitId())) {
                                m.put("comparison", true);
                                m.put("otherCommitId", first.get().getCommitId());
                            } else {
                                m.put("comparison", false);
                            }
                        } else {
                            m.put("comparison", false);
                        }
                    }

                    branchDOs.add(m);
                }
                map.put("branchList", branchDOs);
                list.add(map);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("total", pageData.getTotal());
        result.put("pageSize", pageData.getPageSize());
        result.put("data", list);
        return WebJsonBean.ok(result);
    }

    private static String cut(String str, int length) {
        if (str == null || str.isEmpty()) {
            return "";
        }
        if (str.length() <= length) {
            return "(" + str + ")";
        }
        return "(" + str.substring(0, length) + ")";
    }

    @Synchronized(namespace = "GitClone", keyScript = "args[0]", waitLockMillisecond = 60000)
    @ResponseBody
    @RequestMapping("commit_compare")
    public WebJsonBean commitCompare(Long repositoryId, String commitId, String otherCommitId) {
        WebCheckUtils.assertNotNull(repositoryId, "缺少参数repositoryId");
        WebCheckUtils.assertNotNull(commitId, "缺少参数commitId");
        WebCheckUtils.assertNotNull(otherCommitId, "缺少参数otherCommitId");
        GitRepositoryDO gitRepositoryDO = repositoryService.getById(repositoryId);
        WebCheckUtils.assertNotNull(gitRepositoryDO, "此仓库不存在于数据库中");

        String url = gitRepositoryDO.getUrl();
        int index = gitRepositoryDO.getUrl().lastIndexOf('.');
        if (index != -1) {
            url = gitRepositoryDO.getUrl().substring(0, index);
        }
        url += "/compare/" + commitId + "..." + otherCommitId;
        return WebJsonBean.ok(url);
    }
}