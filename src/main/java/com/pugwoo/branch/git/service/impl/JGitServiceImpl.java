package com.pugwoo.branch.git.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.code_scan.enums.CheckRuleLevelEnum;
import com.pugwoo.branch.git.config.Constants;
import com.pugwoo.branch.git.entity.GitBranchConfigDO;
import com.pugwoo.branch.git.entity.GitIngressBranchDO;
import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.entity.GitTagsDO;
import com.pugwoo.branch.git.enums.IngressBuildStatusEnum;
import com.pugwoo.branch.git.enums.IngressTypeEnum;
import com.pugwoo.branch.git.enums.ItilErrorCode;
import com.pugwoo.branch.git.enums.YesOrNoEnum;
import com.pugwoo.branch.git.model.ConflictResultDTO;
import com.pugwoo.branch.git.model.GitBranchInfoDTO;
import com.pugwoo.branch.git.model.GitUserPasswordDTO;
import com.pugwoo.branch.git.service.ICheckScanningService;
import com.pugwoo.branch.git.service.IGitCloneService;
import com.pugwoo.branch.git.service.IGitIngressBranchService;
import com.pugwoo.branch.git.service.IGitIngressService;
import com.pugwoo.branch.git.service.IGitRepositoryService;
import com.pugwoo.branch.git.service.IGitTagsService;
import com.pugwoo.branch.git.service.IJGitService;
import com.pugwoo.branch.git.utils.GitUtils;
import com.pugwoo.branch.git.utils.PomUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.MergeResult;
import org.eclipse.jgit.api.errors.CheckoutConflictException;
import org.eclipse.jgit.api.errors.RefNotFoundException;
import org.eclipse.jgit.api.errors.WrongRepositoryStateException;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.lib.RepositoryState;
import org.eclipse.jgit.revwalk.RevCommit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class JGitServiceImpl implements IJGitService {

    @Autowired
    private DBHelper dbHelper;
    @Autowired
    private IGitRepositoryService gitRepositoryService;
    @Autowired
    private IGitIngressService gitIngressService;
    @Autowired
    private IGitIngressBranchService gitIngressBranchService;
    @Autowired
    private IGitTagsService gitTagsService;
    @Autowired
    private IGitCloneService gitCloneService;
    @Autowired
    private ICheckScanningService checkScanningService;

    @Value("${branch.filterDevelopBranch:develop-}")
    private String filterDevelopBranch;

    @Override
    public boolean addBranch(GitRepositoryDO gitRepositoryDO,
                             String branchName) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        List<String> developList = GitUtils.getRemoteBranches(git, password);
        if (developList != null && developList.contains(branchName)) {
            throw new AdminInnerException(ItilErrorCode.BRANCH_ERROR);
        }

        GitUtils.createLocalBranchToRemote(git, password,"master", branchName);

        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(gitRepositoryDO.getId());
        gitIngressDO.setType(IngressTypeEnum.BRANCH_NEW.getCode());
        gitIngressDO.setName(branchName);
        gitIngressDO.setCommitId(GitUtils.getCommitId(git, branchName, false));
        gitIngressDO.setMasterCommitId(GitUtils.getCommitId(git, "master", false));
        dbHelper.insert(gitIngressDO);

        return true;
    }

    @Override
    @Synchronized(namespace = "GitClone", keyScript = "args[0].id", waitLockMillisecond = 60000)
    public boolean developBranchMergeMaster(GitRepositoryDO gitRepositoryDO, String branchName) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        GitUtils.checkoutRemoteBranchAndPull(git, password, branchName);

        MergeResult result = git.merge().include(git.getRepository().exactRef("refs/heads/master")).call();
        if(!result.getMergeStatus().isSuccessful()) {
            git.close();
            throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
        }
        if (git.getRepository().getRepositoryState().equals(RepositoryState.MERGING)) {
            git.close();
            throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
        }

        GitUtils.refBranchAndPushRemote(git, password, branchName);
        git.close();
        return true;
    }

    @Override
    @Synchronized(namespace = "GitClone", keyScript = "args[0].id", waitLockMillisecond = 60000)
    public boolean developBranchMergeOtherBranch(GitRepositoryDO gitRepositoryDO,
                                                 String branchName, List<String> otherBranch) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        for (String otherBranchName : otherBranch) {
            GitUtils.checkoutRemoteBranchAndPull(git, password, otherBranchName);
        }
        GitUtils.checkoutRemoteBranchAndPull(git, password, branchName);

        for (String otherBranchName : otherBranch) {
            MergeResult result = git.merge().include(git.getRepository().exactRef("refs/heads/" + otherBranchName)).call();
            if(!result.getMergeStatus().isSuccessful()) {
                git.close();
                throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
            }
            if (git.getRepository().getRepositoryState().equals(RepositoryState.MERGING)) {
                git.close();
                throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
            }
        }

        GitUtils.refBranchAndPushRemote(git, password, branchName);
        git.close();
        return true;
    }

    @Override
    @Synchronized(namespace = "GitClone", keyScript = "args[0].id", waitLockMillisecond = 60000)
    public boolean developBranchMergeRemote(GitRepositoryDO gitRepositoryDO, String branchName, GitBranchConfigDO remoteConfig) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        String remoteName = StringTools.isBlank(remoteConfig.getRemoteName()) ? "autoMergeRemote" : remoteConfig.getRemoteName();
        String remoteBranch = StringTools.isBlank(remoteConfig.getRemoteUrlBranch()) ? "master" : remoteConfig.getRemoteUrlBranch();

        GitUtils.setAutoMergeRemoteUrl(git, remoteName, remoteConfig.getRemoteUrl());
        GitUtils.checkoutRemoteBranchAndPull(git, password, branchName);

        GitUserPasswordDTO remotePassword = new GitUserPasswordDTO();
        remotePassword.setUsername(remoteConfig.getRemoteUrlUsername());
        remotePassword.setPassword(remoteConfig.getRemoteUrlPassword());
        GitUtils.fetchAndMergeRemote(git, remoteName, remoteBranch, remotePassword);

        GitUtils.refBranchAndPushRemoteForce(git, password, branchName);
        git.close();
        return true;
    }

    @Override
    public boolean copyBranch(GitRepositoryDO gitRepositoryDO, String srcBranchName, String branchName) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        List<String> developList = GitUtils.getRemoteBranches(git, password);
        if (developList != null && developList.contains(branchName)) {
            throw new AdminInnerException(ItilErrorCode.BRANCH_ERROR);
        }
        if(developList == null || !developList.contains(srcBranchName)) {
            throw new AdminInnerException(ItilErrorCode.BRANCH_NOT_EXIST);
        }

        GitUtils.createLocalBranchToRemote(git, password, srcBranchName, branchName);

        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(gitRepositoryDO.getId());
        gitIngressDO.setType(IngressTypeEnum.BRANCH_COPY.getCode());
        gitIngressDO.setName(branchName);
        gitIngressDO.setCommitId(GitUtils.getCommitId(git, branchName, false));
        gitIngressDO.setMasterCommitId(GitUtils.getCommitId(git, "master", false));
        dbHelper.insert(gitIngressDO);

        GitIngressBranchDO branchDO = new GitIngressBranchDO();
        branchDO.setIngressId(gitIngressDO.getId());
        branchDO.setBranchName(srcBranchName);
        branchDO.setCommitId(GitUtils.getCommitId(git, srcBranchName, false));
        dbHelper.insert(branchDO);

        return true;
    }

    @Override
    public List<String> getDevelopList(GitRepositoryDO gitRepositoryDO) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        List<String> remoteBranches = GitUtils.getRemoteBranches(git, password);
        if (Objects.equals(filterDevelopBranch, "*")) {
            return ListUtils.filter(remoteBranches, o -> !Objects.equals("master", o));
        } else {
            return ListUtils.filter(remoteBranches, o -> o.startsWith(filterDevelopBranch));
        }
    }

    @HiSpeedCache(keyScript = "args[0].id + args[1]", expireSecond = 60, continueFetchSecond = 3600 * 4)
    @Synchronized(namespace = "GitClone", keyScript = "args[0].id", waitLockMillisecond = 180000)
    @Override
    public List<GitBranchInfoDTO> getDevelopListWithCommitId(GitRepositoryDO gitRepositoryDO, boolean showAllBranch) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        List<GitBranchInfoDTO> remoteBranches = GitUtils.getRemoteBranchesWithCount(git, password);

        List<GitBranchInfoDTO> result = new ArrayList<>();
        for (GitBranchInfoDTO branchInfoDTO : remoteBranches) {
            if ("master".equals(branchInfoDTO.getName())) {
                continue;
            }
            if (showAllBranch || "*".equals(filterDevelopBranch) || branchInfoDTO.getName().startsWith(filterDevelopBranch)) {
                branchInfoDTO.setCommitId(GitUtils.getCommitId(git, branchInfoDTO.getName(), false));
                branchInfoDTO.setMasterCommitId(GitUtils.getCommitId(git, "master", false));
                result.add(branchInfoDTO);
            }
        }

        return result;
    }

    @Override
    public List<String> getAllTagList(GitRepositoryDO gitRepositoryDO) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        return ListUtils.filter(GitUtils.getRemoteTags(git, password),
                o -> o.startsWith(Constants.RELEASE) && !o.startsWith(Constants.RELEASE + Constants.TAG_API));
    }

    @Override
    public List<String> getTagListWithoutMerged(GitRepositoryDO gitRepositoryDO) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        List<String> remoteTags = ListUtils.filter(
                GitUtils.getRemoteTags(git, password), o -> o.startsWith(Constants.RELEASE));

        List<GitTagsDO> mergeList = gitTagsService.getMergeList(gitRepositoryDO.getId());
        if (mergeList != null && mergeList.size() > 0) {
            for (GitTagsDO gitTagsDO : mergeList) {
                remoteTags.remove(gitTagsDO.getTagName());
            }
        }
        return remoteTags;
    }

    @Override
    public boolean deployDevelop(GitRepositoryDO gitRepositoryDO, List<String> develops) throws Exception {
        String branchNameForDevelopLocal = "develop_" + System.currentTimeMillis();

        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        List<GitIngressBranchDO> ingressBranchDOS = new ArrayList<>();
        for (String develop : develops) {
            GitUtils.checkoutRemoteBranchAndPull(git, password, develop);

            GitIngressBranchDO ingressBranchDO = new GitIngressBranchDO();
            ingressBranchDO.setBranchName(develop);
            // 这里拿远程的commitid是因为本地可能因merge而导致commitid不是分支的最新id
            ingressBranchDO.setCommitId(GitUtils.getCommitId(git, develop, false));
            ingressBranchDOS.add(ingressBranchDO);
        }

        GitUtils.createLocalBranchFromMasterAndCheckout(git, branchNameForDevelopLocal);
        for (String develop : develops) {
            MergeResult result = git.merge().include(git.getRepository().exactRef("refs/heads/" + develop)).call();
            if(!result.getMergeStatus().isSuccessful()) {
                git.close();
                throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
            }
        }

        if (git.getRepository().getRepositoryState().equals(RepositoryState.MERGING)) {
            git.close();
            throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
        }

        // 复查合并后的分支是否包含每个分支的最后一个commit id
        List<String> branchCommits = ListUtils.transform(ingressBranchDOS, o -> o.getCommitId());
        Iterable<RevCommit> log = git.log().call();
        for (Iterator<RevCommit> iterator = log.iterator(); iterator.hasNext();) {
            RevCommit rev = iterator.next();
            String commitId = rev.getId().toObjectId().getName();
            while(branchCommits.contains(commitId)) {
                branchCommits.remove(commitId);
            }
            if(branchCommits.isEmpty()) {
                break;
            }
        }
        if(!branchCommits.isEmpty()) {
            throw new AdminInnerException(ItilErrorCode.MERGE_NOT_WORK);
        }

        GitUtils.deleteRemoteTag(git, password, Constants.DEVELOP); //删除远程tag
        GitUtils.deleteLocalTag(git, Constants.DEVELOP); // 因为远程tag会在cloneNew是同步到本地，故再删一遍

        GitUtils.tagAndPushRemote(git, password, Constants.DEVELOP);

        git.close();

        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(gitRepositoryDO.getId());
        gitIngressDO.setType(IngressTypeEnum.DEVELOP.getCode());
        gitIngressDO.setName("develop");
        gitIngressDO.setCommitId(GitUtils.getCommitId(git, branchNameForDevelopLocal, false));
        gitIngressDO.setMasterCommitId(GitUtils.getCommitId(git, "master", false));

        if(gitRepositoryService.isEnableBuildStatusSync(gitRepositoryDO)) {
            gitIngressDO.setBuildStatus(IngressBuildStatusEnum.NEW.getCode());
        }

        gitIngressService.insert(gitIngressDO);
        gitIngressBranchService.addList2(gitIngressDO.getId(), ingressBranchDOS);

        gitRepositoryDO.setLastDevelopIngressId(gitIngressDO.getId());
        dbHelper.update(gitRepositoryDO);

        return true;
    }

    @Override
    public CheckRuleLevelEnum developRelease(GitRepositoryDO gitRepositoryDO, List<String> develops,
                                             String tag) throws Exception {

        // 检查已加发布的tag是否已经合并到master，如果是，则不允许用该tag
        boolean isTagMergedToMaster = dbHelper.isExist(GitTagsDO.class,
                "where repository_id=? and tag_name=? and is_merge_to_master=?",
                gitRepositoryDO.getId(), tag, YesOrNoEnum.YES.getCode());
        if(isTagMergedToMaster) {
            throw new AdminInnerException(ItilErrorCode.TAG_ALREADY_MERGED);
        }

        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        String developName = "branch-for-develop-release-" + System.currentTimeMillis(); // 本地临时的分支

        List<String> localTags = GitUtils.getLocalTags(git);
        for (String branch : localTags) {
            if (branch.startsWith(Constants.RELEASE)) {
                GitUtils.deleteLocalTag(git, branch);
            }
        }

        List<GitIngressBranchDO> ingressBranchDOS = new ArrayList<>();
        for (String develop : develops) {
            GitUtils.checkoutRemoteBranchAndPull(git, password, develop);

            GitIngressBranchDO ingressBranchDO = new GitIngressBranchDO();
            ingressBranchDO.setBranchName(develop);
            ingressBranchDO.setCommitId(GitUtils.getCommitId(git, develop, false));
            ingressBranchDOS.add(ingressBranchDO);
        }

        GitUtils.createLocalBranchFromMasterAndCheckout(git, developName);
        for (String develop : develops) {
            MergeResult result = git.merge().include(git.getRepository().exactRef("refs/heads/" + develop)).call();
            if(!result.getMergeStatus().isSuccessful()) {
                git.close();
                throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
            }
        }
        if (git.getRepository().getRepositoryState().equals(RepositoryState.MERGING)) {
            git.close();
            throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
        }

        // 复查合并后的分支是否包含每个分支的最后一个commit id
        List<String> branchCommits = ListUtils.transform(ingressBranchDOS, o -> o.getCommitId());
        Iterable<RevCommit> log = git.log().call();
        for (Iterator<RevCommit> iterator = log.iterator(); iterator.hasNext();) {
            RevCommit rev = iterator.next();
            String commitId = rev.getId().toObjectId().getName();
            while(branchCommits.contains(commitId)) {
                branchCommits.remove(commitId);
            }
            if(branchCommits.isEmpty()) {
                break;
            }
        }
        if(!branchCommits.isEmpty()) {
            throw new AdminInnerException(ItilErrorCode.MERGE_NOT_WORK);
        }

        GitUtils.deleteRemoteTag(git, password, tag); // 删除远程tag
        GitUtils.deleteLocalTag(git, tag); // 删除本地tag，因为远程可能同步过来
        GitUtils.tagAndPushRemote(git, password, tag);

        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(gitRepositoryDO.getId());
        gitIngressDO.setType(IngressTypeEnum.TEST.getCode());
        gitIngressDO.setName(tag);
        gitIngressDO.setCommitId(GitUtils.getTagCommitId(git, tag));
        gitIngressDO.setMasterCommitId(GitUtils.getCommitId(git, "master", false));

        if(gitRepositoryService.isEnableBuildStatusSync(gitRepositoryDO)) {
            gitIngressDO.setBuildStatus(IngressBuildStatusEnum.NEW.getCode());
        }

        gitIngressService.insert(gitIngressDO);
        gitIngressBranchService.addList2(gitIngressDO.getId(), ingressBranchDOS);

        // 现在的分支是release分支，进行代码检查
        CheckRuleLevelEnum result = checkScanningService.checkScanning(Constants.LOCAL_PATH + gitRepositoryDO.getName(),
                gitIngressDO.getId());

        //插入tag
        GitTagsDO gitTagsDO = new GitTagsDO();
        gitTagsDO.setIngressId(gitIngressDO.getId());
        gitTagsDO.setRepositoryId(gitRepositoryDO.getId());
        gitTagsDO.setTagName(tag);
        gitTagsDO.setIsMergeToMaster(YesOrNoEnum.NO.getCode());
        gitTagsService.insertOrUpdate(gitTagsDO);

        git.close();
        return result;
    }

    @Override
    public boolean mergeToMaster(GitRepositoryDO gitRepositoryDO, String tag,
                                 boolean autoDeleteBranch, boolean forceMerge) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        String oldMasterCommitId = GitUtils.getCommitId(git, "master", false);

        GitUtils.createLocalBranchAndCheckout(git, "refs/tags/" + tag, "tmp_tag");

        git.checkout().setName("master").call();
        MergeResult result = git.merge().include(git.getRepository().exactRef("refs/heads/tmp_tag")).call();
        if(!result.getMergeStatus().isSuccessful()) {
            throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
        }

        // 检查合并之后的commit id对不对
        if(!forceMerge) {
            String commitId1 = GitUtils.getCommitId(git, "tmp_tag", true);
            String commitId2 = GitUtils.getCommitId(git, "master", true);
            if(!commitId1.equals(commitId2)) {
                throw new AdminInnerException(ItilErrorCode.MERGE_NOT_WORK);
            }
        }

        GitUtils.pushMasterToRemote(git, password);
        GitUtils.deleteLocalBranch(git, "tmp_tag");
        git.close();

        List<GitIngressBranchDO> ingressBranchDOS = new ArrayList<>();
        List<String> relatedBranches = new ArrayList<>();
        //tag最后一次对应的开发分支
        GitIngressDO lastOne = gitIngressService.getLastReleaseByName(gitRepositoryDO.getId(), tag);
        if (lastOne != null) {
            ingressBranchDOS = gitIngressBranchService.getList(lastOne.getId());
            for (GitIngressBranchDO gitIngressBranchDO : ingressBranchDOS) {
                relatedBranches.add(gitIngressBranchDO.getBranchName());
            }
        }

        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(gitRepositoryDO.getId());
        gitIngressDO.setType(IngressTypeEnum.PROD.getCode());
        gitIngressDO.setName(tag);
        gitIngressDO.setCommitId(GitUtils.getCommitId(git, "master", false));
        gitIngressDO.setMasterCommitId(oldMasterCommitId);
        gitIngressService.insert(gitIngressDO);
        gitIngressBranchService.addList2(gitIngressDO.getId(), ingressBranchDOS);

        if (autoDeleteBranch) {
            for(String branch : relatedBranches) {
                GitUtils.deleteRemoteBranch(git, password, branch);
            }
        }

        GitTagsDO gitTagsDO = new GitTagsDO();
        gitTagsDO.setIsMergeToMaster(YesOrNoEnum.YES.getCode());
        gitTagsDO.setTagName(tag);
        gitTagsDO.setRepositoryId(gitRepositoryDO.getId());
        gitTagsService.insertOrUpdate(gitTagsDO);
        return true;
    }

    @Override
    public ConflictResultDTO checkConflict(GitRepositoryDO gitRepositoryDO, List<String> develops) throws Exception {
        Git git = getGit(gitRepositoryDO);

        String randomRanchNameForCheckConflict = "branch_for_check_conflict_" + System.currentTimeMillis();

        GitUtils.createLocalBranchFromMasterAndCheckout(git, randomRanchNameForCheckConflict);

        List<String> beforeBranch = new ArrayList<>();
        for (String develop : develops) {
            try {
                GitUtils.checkoutRemoteBranchAndPull(git, pass(gitRepositoryDO), develop);
            } catch (CheckoutConflictException e) { // 检出时代码冲突
                git.close();
                return makeConflictResult(gitRepositoryDO, e.getConflictingPaths(), develop, beforeBranch);
            } catch (RefNotFoundException e) {
                git.close();
                throw new AdminInnerException(ItilErrorCode.BRANCH_DELETE, "选择的分支"+develop+"已经被删，请刷新页面再重新检测");
            }

            git.checkout().setName(randomRanchNameForCheckConflict).call();
            MergeResult result = git.merge().include(git.getRepository().exactRef("refs/heads/" + develop)).call();
            if(!result.getMergeStatus().isSuccessful()) {
                List<String> conflictFiles = ListUtils.toList(result.getConflicts().keySet());
                git.close();
                return makeConflictResult(gitRepositoryDO, conflictFiles, develop, beforeBranch);
            }

            beforeBranch.add(develop);
        }

        if (git.getRepository().getRepositoryState().equals(RepositoryState.MERGING)) {
            git.close();
            throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
        }
        git.close();
        return new ConflictResultDTO();
    }

    private ConflictResultDTO makeConflictResult(GitRepositoryDO gitRepositoryDO, List<String> conflictFiles,
                                                 String currentBranch, List<String> beforeBranch) throws Exception {

        final String branchNameForCheckConflict = "branch_for_check_conflict_" + System.currentTimeMillis();

        Map<String, String> files = new HashMap<>();
        for (String file : conflictFiles) {
            // 读取本地的文件内容出来，如果取不出来就降级为不读
            files.put(file, tryReadFileContent(gitRepositoryDO, file));
        }

        ConflictResultDTO result = new ConflictResultDTO();
        result.setConflict(true);
        result.setFiles(files);

        // 统计一下是哪个分支发生冲突
        List<String> infos = new ArrayList<>();
        result.setConflictInfo(infos);

        if (beforeBranch.isEmpty()) { // 第一个合并就冲突，那就是和主干冲突了
            infos.add(currentBranch + "和master分支发生冲突");
            return result;
        } else {

            // 先排除下是否当前分支和主干有冲突，如果是则提前返回了
            Git git = getGit(gitRepositoryDO);
            GitUtils.createLocalBranchFromMasterAndCheckout(git, branchNameForCheckConflict);

            try {
                GitUtils.checkoutRemoteBranchAndPull(git, pass(gitRepositoryDO), currentBranch);
            } catch (CheckoutConflictException e) { // 检出时代码冲突
                git.close();
                infos.add(currentBranch + "和master分支发生冲突");
                return result;
            }

            git.checkout().setName(branchNameForCheckConflict).call();
            MergeResult mr = git.merge().include(git.getRepository().exactRef("refs/heads/" + currentBranch)).call();
            if(!mr.getMergeStatus().isSuccessful()) {
                git.close();
                infos.add(currentBranch + "和master分支发生冲突");
                return result;
            }

            git.close();

            // 如果不是，则是两个分支之间的冲突
            if (beforeBranch.size() == 1) {
                infos.add("分支" + beforeBranch.get(0) + "和分支" + currentBranch + "有冲突");
                return result;
            } else {
                // 对于之前的分支，一个一个测试看哪两个冲突了
                for (String branch : beforeBranch) {
                    git = getGit(gitRepositoryDO);
                    GitUtils.createLocalBranchFromMasterAndCheckout(git, branchNameForCheckConflict);

                    GitUtils.checkoutRemoteBranchAndPull(git, pass(gitRepositoryDO), branch); // 这个是不会冲突的
                    git.checkout().setName(branchNameForCheckConflict).call();
                    // 这个也不会冲突
                    git.merge().include(git.getRepository().exactRef("refs/heads/" + branch)).call();

                    try {
                        GitUtils.checkoutRemoteBranchAndPull(git, pass(gitRepositoryDO), currentBranch);
                    } catch (CheckoutConflictException | WrongRepositoryStateException e) { // 检出时代码冲突
                        git.close();
                        infos.add("分支" + branch + "和分支" + currentBranch + "有冲突");
                        break;
                    }

                    git.checkout().setName(branchNameForCheckConflict).call();
                    mr = git.merge().include(git.getRepository().exactRef("refs/heads/" + currentBranch)).call();
                    if(!mr.getMergeStatus().isSuccessful()) {
                        git.close();
                        infos.add("分支" + branch + "和分支" + currentBranch + "有冲突");
                        break;
                    }

                    git.close();
                }

                return result;
            }
        }
    }

    private String tryReadFileContent(GitRepositoryDO gitRepositoryDO, String file) {
        String path = Constants.LOCAL_PATH + "/" + gitRepositoryDO.getName() + "/" + file;
        try {
            return IOUtils.readAllAndClose(new FileInputStream(path), "utf-8");
        } catch (Exception e) {
            log.error("read repo:{} local file path:{} fail", gitRepositoryDO.getName(), path);
            return "";
        }
    }

    @Override
    public boolean compareRelease(GitRepositoryDO gitRepositoryDO, List<String> develops, String tag) throws Exception {
        Git git = getGit(gitRepositoryDO);

        List<String> remoteTags = GitUtils.getRemoteTags(git, pass(gitRepositoryDO));
        if (!remoteTags.contains(tag)) {
            throw new AdminInnerException(ItilErrorCode.NO_SUCH_TAG); //判断线上是否存在发布版本
        }

        //新增
        GitUtils.deleteRemoteTag(git, pass(gitRepositoryDO), Constants.TAG_TEMP_NEW_RELEASE);
        GitUtils.createLocalBranchFromMasterAndCheckout(git, Constants.TAG_TEMP_NEW_RELEASE + "-branch");
        for (String develop : develops) {
            try {
                GitUtils.checkoutRemoteBranchAndPull(git, pass(gitRepositoryDO), develop);
            } catch (CheckoutConflictException e) { // 检出时代码冲突
                throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
            }

            git.checkout().setName(Constants.TAG_TEMP_NEW_RELEASE+ "-branch").call();
            MergeResult result = git.merge().include(git.getRepository().exactRef("refs/heads/" + develop)).call();
            if(!result.getMergeStatus().isSuccessful()) {
                throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
            }
        }
        if (git.getRepository().getRepositoryState().equals(RepositoryState.MERGING)) {
            git.close();
            throw new AdminInnerException(ItilErrorCode.MERGING_ERROR);
        }

        GitUtils.deleteLocalTag(git, Constants.TAG_TEMP_NEW_RELEASE);
        GitUtils.tagAndPushRemote(git, pass(gitRepositoryDO), Constants.TAG_TEMP_NEW_RELEASE);

        git.close();
        return true;
    }

    @Override
    public boolean deleteBranch(GitRepositoryDO gitRepositoryDO, String branchName) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        String commitId = GitUtils.getCommitId(git, branchName, false);

        GitUtils.deleteLocalBranch(git, branchName);
        GitUtils.deleteRemoteBranch(git, password, branchName);

        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(gitRepositoryDO.getId());
        gitIngressDO.setType(IngressTypeEnum.BRANCH_DEL.getCode());
        gitIngressDO.setName(branchName);
        gitIngressDO.setCommitId(commitId);
        gitIngressDO.setMasterCommitId(GitUtils.getCommitId(git, "master", false));
        dbHelper.insert(gitIngressDO);

        git.close();
        return true;
    }

    @Override
    public String getBranchSnapshotApiVersion(GitRepositoryDO gitRepositoryDO, String branchName) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        GitUtils.createLocalBranchAndCheckout(git, "refs/remotes/origin/" + branchName, branchName);
        GitUtils.pull(git, password);

        return getMavenVersion(gitRepositoryDO);
    }

    /**获得当前git分支下的pom版本，请先切换分支再调用该方法*/
    private String getMavenVersion(GitRepositoryDO gitRepositoryDO) throws Exception {
        String localPath = Constants.LOCAL_PATH + gitRepositoryDO.getName();
        String apiPomLocation = gitRepositoryDO.getApiPomLocation();
        if(!apiPomLocation.startsWith("/")) {
            apiPomLocation = "/" + apiPomLocation;
        }
        File file = new File(localPath + apiPomLocation);
        if(!file.exists()) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "填写的pom文件:" + apiPomLocation + "不存在");
        }
        if(!file.isFile()) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "填写的pom文件:" + apiPomLocation + "不是普通文件");
        }

        return PomUtils.getVersion(IOUtils.readAllAndClose(new FileInputStream(file), "utf-8"));
    }

    @Override
    public boolean releaseBranchSnapshotApi(GitRepositoryDO gitRepositoryDO, String branchName) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        GitUtils.createLocalBranchAndCheckout(git, "refs/remotes/origin/" + branchName, branchName);
        GitUtils.pull(git, password);

        String mavenVersion = getMavenVersion(gitRepositoryDO);
        if(mavenVersion == null || !mavenVersion.endsWith("-SNAPSHOT")) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "当前pom文件非SNAPSHOT版本");
        }

        String apiTagName = Constants.TAG_API + branchName;
        GitUtils.deleteLocalTag(git, apiTagName);
        GitUtils.deleteRemoteTag(git, password, apiTagName);
        GitUtils.tagAndPushRemote(git, password, apiTagName);

        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(gitRepositoryDO.getId());
        gitIngressDO.setType(IngressTypeEnum.DEVELOP_API.getCode());
        gitIngressDO.setName(mavenVersion);
        gitIngressDO.setCommitId(GitUtils.getTagCommitId(git, apiTagName));
        gitIngressDO.setMasterCommitId(GitUtils.getCommitId(git, "master", false));
        dbHelper.insert(gitIngressDO);

        GitIngressBranchDO branchDO = new GitIngressBranchDO();
        branchDO.setIngressId(gitIngressDO.getId());
        branchDO.setBranchName(branchName);
        branchDO.setCommitId(GitUtils.getCommitId(git, branchName, false));
        dbHelper.insert(branchDO);

        git.close();
        return true;
    }

    @Override
    public boolean deleteTag(GitRepositoryDO gitRepositoryDO, List<String> tags) throws Exception {
        if(tags == null || tags.isEmpty()) {
            return true;
        }

        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        for (String tag : tags) {

            GitIngressDO gitIngressDO = new GitIngressDO();
            gitIngressDO.setRepositoryId(gitRepositoryDO.getId());
            gitIngressDO.setType(IngressTypeEnum.TEST_TAG_DEL.getCode());
            gitIngressDO.setName(tag);
            gitIngressDO.setCommitId(GitUtils.getTagCommitId(git, tag));
            gitIngressDO.setMasterCommitId(GitUtils.getCommitId(git, "master", false));
            dbHelper.insert(gitIngressDO);

            GitUtils.deleteRemoteTag(git, password, tag);
        }

        // 删除数据库中的tags记录
        dbHelper.delete(GitTagsDO.class, "where repository_id=? and tag_name in (?)",
                gitRepositoryDO.getId(), tags);
        return true;
    }

    @Override
    public String getReleaseApiVersion(GitRepositoryDO gitRepositoryDO) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        getGit(gitRepositoryDO); // 切换到master

        return getMavenVersion(gitRepositoryDO);
    }

    @Override
    public boolean releaseApi(GitRepositoryDO gitRepositoryDO, String apiVersion) throws Exception {
        if(apiVersion == null || apiVersion.endsWith("-SNAPSHOT")) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "当前pom API版本非正式版本：" + apiVersion);
        }

        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);

        String tagName = Constants.TAG_API + apiVersion;

        GitUtils.deleteRemoteTag(git, password, tagName); //删除远程tag
        GitUtils.deleteLocalTag(git, tagName);
        GitUtils.tagAndPushRemote(git, password, tagName);

        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(gitRepositoryDO.getId());
        gitIngressDO.setType(IngressTypeEnum.PROD_API.getCode());
        gitIngressDO.setName(apiVersion);
        gitIngressDO.setCommitId(GitUtils.getTagCommitId(git, tagName));
        gitIngressDO.setMasterCommitId(GitUtils.getCommitId(git, "master", false));
        dbHelper.insert(gitIngressDO);

        git.close();
        return true;
    }
    
    /**
     * 回退版本
     * @param gitRepositoryDO 仓库
     * @param branchName      分支名
     * @param ahead           回退几个版本
     * @return true 成功
     */
    @Override
    public boolean resetCommit(GitRepositoryDO gitRepositoryDO, String branchName, int ahead) throws Exception {
        GitUserPasswordDTO password = pass(gitRepositoryDO);
        Git git = getGit(gitRepositoryDO);
        try {
            GitUtils.resetCommit(git, password, branchName, ahead);
        } finally {
            git.close();
        }
        
        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setRepositoryId(gitRepositoryDO.getId());
        gitIngressDO.setType(IngressTypeEnum.BRANCH_RESET.getCode());
        gitIngressDO.setName(branchName);
        gitIngressDO.setCommitId(GitUtils.getCommitId(git, branchName, false));
        gitIngressDO.setMasterCommitId(GitUtils.getCommitId(git, "master", false));
        dbHelper.insert(gitIngressDO);
        
        return true;
    }
    
    private GitUserPasswordDTO pass(GitRepositoryDO gitRepositoryDO) {
        GitUserPasswordDTO gitUserPasswordDTO = new GitUserPasswordDTO();
        gitUserPasswordDTO.setUsername(gitRepositoryDO.getUsername());
        gitUserPasswordDTO.setPassword(gitRepositoryDO.getPassword());
        return gitUserPasswordDTO;
    }

    /**拿到git对象，此时仓库已经确保是最新的了*/
    private Git getGit(GitRepositoryDO gitRepositoryDO) throws Exception {
        String localPath = Constants.LOCAL_PATH + gitRepositoryDO.getName();

        gitCloneService.cloneNew(gitRepositoryDO);

        Repository repository = GitUtils.getJRepository(localPath);
        return new Git(repository);
    }

}
