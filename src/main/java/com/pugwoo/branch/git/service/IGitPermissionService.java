package com.pugwoo.branch.git.service;

import java.util.List;

/**
 * 权限相关的接口
 */
public interface IGitPermissionService {

    /**是否打开了权限*/
    boolean isCheckPermission();

    /**刷新仓库权限*/
    void addRepo(String droneBaseUrl, String username, String password, Long userId);

    List<String> getDroneBaseUrls();

    /**获得所有允许的仓库url，每个仓库分带.git和不带两份*/
    List<String> getAllowedRepo(Long loginUserId);

}
