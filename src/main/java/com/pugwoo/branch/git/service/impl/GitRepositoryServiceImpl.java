package com.pugwoo.branch.git.service.impl;

import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.service.IGitPermissionService;
import com.pugwoo.branch.git.service.IGitRepositoryService;
import com.pugwoo.branch.git.vo.GitRepo4BuildStatusVO;
import com.pugwoo.branch.git.vo.GitRepositoryVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class GitRepositoryServiceImpl implements IGitRepositoryService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;
    @Autowired
    private IGitPermissionService gitPermissionService;

    @Value("${branch.mockCIDISync:false}")
    private Boolean mockCIDISync;

    @Override
    public GitRepositoryDO getById(Long id) {
        if (id == null) {
            return null;
        }
        return dbHelper.getByKey(GitRepositoryDO.class, id);
    }

    @Override
    public GitRepositoryDO getByName(String name) {
        if (name == null) {
            return null;
        }
        return dbHelper.getOne(GitRepositoryDO.class, "where name=?", name);
    }

    @Override
    public PageData<GitRepositoryVO> getPage(int page, int pageSize, String name, Long loginUserId, boolean isAdmin) {
        StringBuilder where = new StringBuilder("where 1=1");
        List<Object> params = new ArrayList<>();
        if (StringTools.isNotBlank(name)) {
            where.append(" and name like ?");
            params.add("%" + name.trim() + "%");
        }

        if(!isAdmin) { // 管理员不受此限制
            if (gitPermissionService.isCheckPermission()) {
                where.append(" and url in (?)");
                params.add(gitPermissionService.getAllowedRepo(loginUserId));
            }
        }

        where.append(" order by seq, id");
        return dbHelper.getPage(GitRepositoryVO.class, page, pageSize, where.toString(), params.toArray());
    }

    @Override
    public PageData<GitRepo4BuildStatusVO> getBuildStatus(int page, int pageSize, String name) {
        StringBuilder where = new StringBuilder("where 1=1");
        List<Object> params = new ArrayList<>();
        if (StringTools.isNotBlank(name)) {
            where.append(" and name like ?");
            params.add("%" + name.trim() + "%");
        }
        where.append(" order by seq, id");
        return dbHelper.getPage(GitRepo4BuildStatusVO.class, page, pageSize, where.toString(), params.toArray());
    }

    @Override
    public GitRepositoryDO insertOrUpdate(GitRepositoryDO gitRepositoryDO) {
        // 如果是新增且没有主动设置seq，则设置最大的seq值加上10
        if (gitRepositoryDO.getId() == null && gitRepositoryDO.getSeq() == null) {
            GitRepositoryDO maxSeq = dbHelper.getOne(GitRepositoryDO.class, "order by seq desc");
            int max = 0;
            if (maxSeq != null && maxSeq.getSeq() != null) {
                max = maxSeq.getSeq();
            }
            gitRepositoryDO.setSeq(max + 100);
        }

        dbHelper.insertOrUpdate(gitRepositoryDO);
        return gitRepositoryDO;
    }

    @Override
    public boolean deleteById(Long id) {
        if (id == null) {
            return false;
        }

        GitRepositoryDO gitRepositoryDO = new GitRepositoryDO();
        gitRepositoryDO.setId(id);
        return dbHelper.delete(gitRepositoryDO) > 0;
    }

    /**
     * @Description: 查询所有仓库
     * @date: 2019-10-17
     */
    @Override
    public List<GitRepositoryDO> getAll() {
        return dbHelper.getAll(GitRepositoryDO.class);
    }

    @Override
    public boolean isEnableBuildStatusSync(GitRepositoryDO gitRepositoryDO) {
        return mockCIDISync || StringTools.isNotBlank(gitRepositoryDO.getDroneBaseUrl());
    }
}