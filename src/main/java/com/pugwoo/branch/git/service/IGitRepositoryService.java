package com.pugwoo.branch.git.service;

import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.vo.GitRepo4BuildStatusVO;
import com.pugwoo.branch.git.vo.GitRepositoryVO;
import com.pugwoo.dbhelper.model.PageData;

import java.util.List;

public interface IGitRepositoryService {
    /**
     * 通过主键获得数据
     */
    GitRepositoryDO getById(Long id);


    GitRepositoryDO getByName(String name);

    /**
     * 获得分页数据
     *
     * @param page     页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     * @param name     仓库名称
     */
    PageData<GitRepositoryVO> getPage(int page, int pageSize, String name, Long loginUserId, boolean isAdmin);

    /**
     * 获得仓库的编译状态
     * @param page
     * @param pageSize
     * @param name
     * @return
     */
    PageData<GitRepo4BuildStatusVO> getBuildStatus(int page, int pageSize, String name);

    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    GitRepositoryDO insertOrUpdate(GitRepositoryDO gitRepositoryDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

    /**
     *
     * 查询所有仓库
     */
    List<GitRepositoryDO> getAll();

    /**
     * 判断是否进行编译状态同步
     */
    boolean isEnableBuildStatusSync(GitRepositoryDO gitRepositoryDO);

}
