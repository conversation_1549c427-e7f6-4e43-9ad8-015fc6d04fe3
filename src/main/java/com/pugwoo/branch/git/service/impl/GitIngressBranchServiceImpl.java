package com.pugwoo.branch.git.service.impl;

import com.pugwoo.branch.git.entity.GitIngressBranchDO;
import com.pugwoo.branch.git.service.IGitIngressBranchService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class GitIngressBranchServiceImpl implements IGitIngressBranchService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Override
    public GitIngressBranchDO getById(Long id) {
        if (id == null) {
            return null;
        }
        return dbHelper.getByKey(GitIngressBranchDO.class, id);
    }

    @Override
    public PageData<GitIngressBranchDO> getPage(int page, int pageSize) {
        return dbHelper.getPage(GitIngressBranchDO.class, page, pageSize);
    }

    @Override
    public void insert(GitIngressBranchDO gitIngressBranchDO) {
        if (gitIngressBranchDO == null) {
            return;
        }
        dbHelper.insertOrUpdate(gitIngressBranchDO);
        return;
    }

    @Override
    public boolean deleteById(Long id) {
        if (id == null) {
            return false;
        }
        GitIngressBranchDO gitIngressBranchDO = new GitIngressBranchDO();
        gitIngressBranchDO.setId(id);
        return dbHelper.delete(gitIngressBranchDO) > 0;
    }

    @Override
    public List<GitIngressBranchDO> getList(Long ingressId) {
        String sql = "where ingress_id = ? ";
        List<Object> params = new ArrayList<>();
        params.add(ingressId);
        return dbHelper.getAll(GitIngressBranchDO.class, sql, params);
    }

    @Override
    public void addList2(Long ingressId, List<GitIngressBranchDO> branchList) {
        for(GitIngressBranchDO ingressBranchDO : branchList) {
            ingressBranchDO.setId(null);
            ingressBranchDO.setIngressId(ingressId);
        }
        dbHelper.insert(branchList);
    }
}