package com.pugwoo.branch.git.service.impl;

import com.pugwoo.branch.git.entity.GitIngressDO;
import com.pugwoo.branch.git.enums.IngressTypeEnum;
import com.pugwoo.branch.git.service.IGitIngressService;
import com.pugwoo.branch.git.vo.GitIngressVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class GitIngressServiceImpl implements IGitIngressService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Override
    public GitIngressDO getById(Long id) {
        if (id == null) {
            return null;
        }
        return dbHelper.getByKey(GitIngressDO.class, id);
    }

    @Override
    public PageData<GitIngressDO> getPage(int page, int pageSize) {
        return dbHelper.getPage(GitIngressDO.class, page, pageSize);
    }

    @Override
    public GitIngressDO insert(GitIngressDO gitIngressDO) {
        if (gitIngressDO == null) {
            return null;
        }
        dbHelper.insertOrUpdate(gitIngressDO);
        return gitIngressDO;
    }

    @Override
    public boolean deleteById(Long id) {
        if (id == null) {
            return false;
        }
        GitIngressDO gitIngressDO = new GitIngressDO();
        gitIngressDO.setId(id);
        return dbHelper.delete(gitIngressDO) > 0;
    }

    @Override
    public GitIngressDO getLastReleaseByName(Long repositoryId, String name) {
        String sql = "where repository_id=? and name=? and type=? ORDER BY create_time DESC";
        List<Object> params = new ArrayList<>();
        params.add(repositoryId);
        params.add(name);
        params.add(IngressTypeEnum.TEST.getCode());
        return dbHelper.getOne(GitIngressDO.class, sql, params.toArray());
    }

    @Override
    public PageData<GitIngressVO> getPageWith(Long repositoryId, List<IngressTypeEnum> typeEnums, int page, int pageSize) {
        StringBuilder sql = new StringBuilder("where repository_id = ?");
        List<Object> params = ListUtils.newArrayList(repositoryId);
        if (typeEnums != null && !typeEnums.isEmpty()) {
            List<String> types = ListUtils.transform(typeEnums, o -> o.getCode());
            sql.append(" and type in (?)");
            params.add(types);
        }

        sql.append(" ORDER BY create_time DESC");
        return dbHelper.getPage(GitIngressVO.class, page, pageSize, sql.toString(), params.toArray());
    }

    @Override
    public GitIngressVO getLastOne(Long repositoryId, IngressTypeEnum typeEnum) {
        String sql = "where repository_id = ? and type = ?  ORDER BY create_time DESC ";
        List<Object> params = new ArrayList<>();
        params.add(repositoryId);
        params.add(typeEnum.getCode());
        return dbHelper.getOne(GitIngressVO.class, sql, params.toArray());
    }

    @Override
    public Map<String, GitIngressVO> getTagBranches(Long repositoryId, List<String> tagNames) {
        if (repositoryId == null || ListUtils.isEmpty(tagNames)) {
            return new HashMap<>();
        }
        List<GitIngressVO> ingress = dbHelper.getAll(GitIngressVO.class,
                "where id in (SELECT ingress_id FROM git_tags WHERE deleted=0 AND repository_id=? AND tag_name in (?))",
                repositoryId, tagNames);
        return ListUtils.toMap(ingress, o -> o.getName(), o -> o);
    }
}