package com.pugwoo.branch.git.service.impl;

import com.pugwoo.branch.git.entity.GitBranchConfigDO;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.service.GitBranchService;
import com.pugwoo.dbhelper.DBHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GitBranchServiceImpl implements GitBranchService {

    @Autowired
    private DBHelper adminDBHelper;

    @Override
    public void switchAutoMergeMaster(GitRepositoryDO gitRepositoryDO, String branchName, boolean isAutoMergeMaster) {
        GitBranchConfigDO one = adminDBHelper.getOne(GitBranchConfigDO.class, "where repo_id=? and branch_name=?",
                gitRepositoryDO.getId(), branchName);
        if (one != null) {
            one.setIsAutoMergeMaster(isAutoMergeMaster);
            one.setIsMergeMasterFail(false); // 重新开启时，清理合并失败的记录
            adminDBHelper.update(one);
        } else {
            one = new GitBranchConfigDO();
            one.setRepoId(gitRepositoryDO.getId());
            one.setBranchName(branchName);
            one.setIsAutoMergeMaster(isAutoMergeMaster);
            one.setIsMergeMasterFail(false);
            adminDBHelper.insert(one);
        }
    }

}
