package com.pugwoo.branch.git.service;

import com.pugwoo.branch.git.entity.GitRepositoryDO;

/**
 * 之所以独立出来，是想使用AOP
 */
public interface IGitCloneService {

    /**
     * 下载新仓库，下载失败抛出异常
     * @param
     * @return
     */
    void cloneNew(GitRepositoryDO gitRepositoryDO) throws Exception;

    /**
     * 复制仓库，返回复制之后的随机目录，使用后请删除掉
     * @param gitRepositoryDO
     * @return 返回的字符串
     * @throws Exception
     */
    String copyRepo(GitRepositoryDO gitRepositoryDO) throws Exception;

}
