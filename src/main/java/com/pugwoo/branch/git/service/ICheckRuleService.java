package com.pugwoo.branch.git.service;

import com.pugwoo.branch.code_scan.model.GitCodeCheckRuleBO;
import com.pugwoo.branch.git.model.CheckRuleCreateDTO;
import com.pugwoo.dbhelper.model.PageData;

public interface ICheckRuleService {

    /**
     * 新增校验规则
     *
     * @param gitCheckCreateDTO
     * @return
     */
    void add(CheckRuleCreateDTO gitCheckCreateDTO);

    /**
     * 查看校验规则列表
     *
     * @param name 规则名称
     * @param page 页数
     * @param pageSize 每页个数
     * @return
     */
    PageData<GitCodeCheckRuleBO> getPage(String name, int page, int pageSize);
    /**
     * 根据id获取校验规则
     *
     * @param id 主键
       @return
    */
    GitCodeCheckRuleBO get(Long id);
}
