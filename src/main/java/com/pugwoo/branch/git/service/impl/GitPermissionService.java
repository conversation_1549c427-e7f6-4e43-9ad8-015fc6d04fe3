package com.pugwoo.branch.git.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.git.entity.GitUserRepoDO;
import com.pugwoo.branch.git.service.IGitPermissionService;
import com.pugwoo.branch.git.utils.drone.DroneRepoDTO;
import com.pugwoo.branch.git.utils.drone.DroneUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class GitPermissionService implements IGitPermissionService {

    @Value("${branch.enableDroneRepoControl:false}")
    private Boolean enableDroneRepoControl;

    @Value("${branch.droneBaseUrlList:}")
    private String[] droneUrlList;

    @Autowired
    private DBHelper dbHelper;

    @Override
    public boolean isCheckPermission() {
        return enableDroneRepoControl != null && enableDroneRepoControl;
    }

    @Override
    public void addRepo(String droneBaseUrl, String username, String password, Long userId) {
        // 这里有一个约定，用户的Drone的账号和密码和git一样
        String token = DroneUtils.getToken(droneBaseUrl, username, password);
        if (StringTools.isBlank(token)) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "drone账号或密码错误");
        }
        List<DroneRepoDTO> repos = DroneUtils.getAllRepoCloneUrlList(droneBaseUrl, token);

        for(DroneRepoDTO repo : repos) {
            if(!dbHelper.isExist(GitUserRepoDO.class, "where user_id=? and url=?",
                    userId, repo.getLinkUrl())) {
                GitUserRepoDO gitUserRepoDO = new GitUserRepoDO();
                gitUserRepoDO.setUserId(userId);
                gitUserRepoDO.setUrl(repo.getLinkUrl());
                dbHelper.insert(gitUserRepoDO);
            }
        }
    }

    @Override
    public List<String> getDroneBaseUrls() {
        if(droneUrlList == null || droneUrlList.length == 0) {
            return new ArrayList<>();
        }
        return ListUtils.newArrayList(droneUrlList);
    }

    @Override
    public List<String> getAllowedRepo(Long loginUserId) {
        List<GitUserRepoDO> all = dbHelper.getAll(GitUserRepoDO.class, "where user_id=?", loginUserId);
        List<String> result = new ArrayList<>();
        for (GitUserRepoDO repoDO : all) {
            String url = repoDO.getUrl();
            if(url.endsWith(".git")) {
                result.add(url);
                result.add(url.substring(0, url.length() - 4));
            } else {
                result.add(url);
                result.add(url + ".git");
            }
        }
        return result;
    }

}
