package com.pugwoo.branch.git.service;

import com.pugwoo.branch.git.entity.GitTagsDO;
import com.pugwoo.dbhelper.model.PageData;

import java.util.List;

public interface IGitTagsService {

    /**
     * 通过主键获得数据
     */
    GitTagsDO getById(Long id);

    /**
     * 获得分页数据
     *
     * @param page     页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     */
    PageData<GitTagsDO> getPage(int page, int pageSize);

    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    GitTagsDO insertOrUpdate(GitTagsDO gitTagsDO);

    /**
     * 查询已被合并至master的tag
     *
     * @param
     * @return
     */
    List<GitTagsDO> getMergeList(Long repositoryId);
}
