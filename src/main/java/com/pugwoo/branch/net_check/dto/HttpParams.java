package com.pugwoo.branch.net_check.dto;


import lombok.Data;

import java.util.Map;

/**
 * 适合于http的请求参数
 */
@Data
public class HttpParams {

    /**
     * http请求的头部
     */
    private Map<String, String> header;

    /**
     * GET和POST query param的参数
     */
    private Map<String, Object> param;

    /**
     * http请求的body
     */
    private byte[] body;

    /**
     * 请求和返回的字符集
     */
    private String charset;

}
