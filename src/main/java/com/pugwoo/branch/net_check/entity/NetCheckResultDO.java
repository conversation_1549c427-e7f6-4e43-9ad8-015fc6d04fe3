package com.pugwoo.branch.net_check.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 检查net结果表
 */
@Data
@ToString
@Table("net_check_result")
public class NetCheckResultDO extends AdminCoreDO {

    /** net检查配置表id<br/>Column: [net_check_config_id] */
    @Column(value = "net_check_config_id")
    private Long netCheckConfigId;

    /** 网络类型，如HTTP、TCP、PING<br/>Column: [net_type] */
    @Column(value = "net_type")
    private String netType;

    /** 请求方式，例如http的话有GET POST<br/>Column: [method] */
    @Column(value = "method")
    private String method;

    /** 请求url或唯一连接标识<br/>Column: [uri] */
    @Column(value = "uri")
    private String uri;

    /** 请求体，默认用json格式，实际情况根据net_type来<br/>Column: [params] */
    @Column(value = "params")
    private String params;

    /** 本次执行次数<br/>Column: [times] */
    @Column(value = "times")
    private Integer times;

    /** 本次检查断言表达式<br/>Column: [assertion] */
    @Column(value = "assertion")
    private String assertion;

    /** 本次检查总耗时 毫秒<br/>Column: [cost_time_ms] */
    @Column(value = "cost_time_ms")
    private Integer costTimeMs;

    /** 本次检查返回值，如循环多次，存其中1次结果，如超过4096，截断<br/>Column: [response_content] */
    @Column(value = "response_content")
    private String responseContent;

    /** 本次检查返回字节数，总数<br/>Column: [response_bytes] */
    @Column(value = "response_bytes")
    private Long responseBytes;

    /** 本次检查返回的速度，byte每秒<br/>Column: [response_bps] */
    @Column(value = "response_bps")
    private Integer responseBps;

    /** 本次检查返回速度峰值，byte每秒<br/>Column: [response_peak_bps] */
    @Column(value = "response_peak_bps")
    private Integer responsePeakBps;

    /** 执行是否成功 1成功 0失败<br/>Column: [is_success] */
    @Column(value = "is_success")
    private Boolean isSuccess;

    /** 错误信息<br/>Column: [error_msg] */
    @Column(value = "error_msg")
    private String errorMsg;

}