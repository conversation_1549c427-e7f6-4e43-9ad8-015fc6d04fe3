package com.pugwoo.branch.net_check.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.net_check.entity.NetCheckResultDO;
import com.pugwoo.branch.net_check.service.NetCheckResultService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NetCheckResultServiceImpl implements NetCheckResultService {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public NetCheckResultDO getById(Long id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(NetCheckResultDO.class, id);
    }

    @Override
    public PageData<NetCheckResultDO> getPage(int page, int pageSize, Long configId) {
        WhereSQL where = new WhereSQL();
        where.andIf(configId != null, "net_check_config_id=?", configId);
        where.addOrderBy("id desc");
        return dbHelper.getPage(NetCheckResultDO.class, page, pageSize,
                where.getSQL(), where.getParams());
    }

    @Override
    public ResultBean<Long> insertOrUpdate(NetCheckResultDO netCheckResultDO) {
        if(netCheckResultDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(netCheckResultDO);
        return rows > 0 ? ResultBean.ok(netCheckResultDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        NetCheckResultDO netCheckResultDO = new NetCheckResultDO();
        netCheckResultDO.setId(id);
        return dbHelper.delete(netCheckResultDO) > 0;
    }

}