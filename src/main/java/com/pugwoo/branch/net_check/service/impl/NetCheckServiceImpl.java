package com.pugwoo.branch.net_check.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.net_check.dto.HttpParams;
import com.pugwoo.branch.net_check.entity.NetCheckConfigDO;
import com.pugwoo.branch.net_check.entity.NetCheckResultDO;
import com.pugwoo.branch.net_check.enums.NetTypeEnum;
import com.pugwoo.branch.net_check.service.NetCheckService;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import org.mvel2.MVEL;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class NetCheckServiceImpl implements NetCheckService {

    @Override
    public void httpCheck(NetCheckConfigDO netCheckConfigDO, NetCheckResultDO resultDO) {
        if (netCheckConfigDO == null || !NetTypeEnum.HTTP.getCode().equals(netCheckConfigDO.getNetType())) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "unsupported netType");
        }
        if (StringTools.isBlank(netCheckConfigDO.getUri())) {
            throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "uri is blank");
        }

        String method = resultDO.getMethod();
        if (StringTools.isBlank(method)) {
            method = "GET";
        }

        Integer connectTimeoutMs = netCheckConfigDO.getConnectTimeoutMs();
        if (connectTimeoutMs == null) {
            connectTimeoutMs = 30000;
        }
        Integer readTimeoutMs = netCheckConfigDO.getReadTimeoutMs();
        if (readTimeoutMs == null) {
            readTimeoutMs = 60000;
        }
        Integer times = netCheckConfigDO.getTimes();
        if (times == null) {
            times = 1;
        }

        HttpParams params = JSON.parse(netCheckConfigDO.getParams(), HttpParams.class);

        long start = System.currentTimeMillis();
        long bytes = 0L;
        try {
            for (int i = 0; i < times; i++) {
                Browser browser = new Browser();
                browser.setConnectTimeoutSeconds(connectTimeoutMs / 1000);
                browser.setReadTimeoutSeconds(readTimeoutMs / 1000);

                HttpResponse resp = null;
                if ("GET".equalsIgnoreCase(method)) {
                    if (params != null && params.getParam() != null) {
                        resp = browser.get(netCheckConfigDO.getUri(), params.getParam());
                    } else {
                        resp = browser.get(netCheckConfigDO.getUri());
                    }
                } else if ("POST".equalsIgnoreCase(method)) {
                    // TODO
                }

                bytes += resp == null || resp.getContentBytes() == null ? 0 : resp.getContentBytes().length;

                // 断言
                if (StringTools.isNotBlank(netCheckConfigDO.getAssertion())) {
                    Map<String, Object> vars = new HashMap<>(); // 给断言用
                    vars.put("code", resp == null ? null : resp.getResponseCode());
                    vars.put("body", resp == null ? null : resp.getContentString());

                    try {
                        Object result = MVEL.eval(netCheckConfigDO.getAssertion().trim(), vars);
                        if (result instanceof Boolean) {
                            resultDO.setIsSuccess((boolean) result);
                            resultDO.setErrorMsg("断言结果: " + JSON.toJson(result));
                        } else {
                            resultDO.setIsSuccess(false);
                            resultDO.setErrorMsg("断言结果不是boolean: " + JSON.toJson(result));
                        }
                    } catch (RuntimeException e) {
                        resultDO.setIsSuccess(false);
                        resultDO.setErrorMsg("断言发生异常: " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            resultDO.setIsSuccess(false);
            resultDO.setErrorMsg(e.getMessage());
        }

        long end = System.currentTimeMillis();
        resultDO.setCostTimeMs((int)(end - start));
        resultDO.setResponseBytes(bytes);
        if (resultDO.getCostTimeMs() <= 0) {
            resultDO.setResponseBps((int)(bytes / 0.001));
        } else {
            resultDO.setResponseBps((int)(bytes / resultDO.getCostTimeMs() * 1000));
        }
    }

}
