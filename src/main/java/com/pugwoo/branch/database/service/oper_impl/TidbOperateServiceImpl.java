package com.pugwoo.branch.database.service.oper_impl;

import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.enums.DatabaseTypeEnum;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> <br>
 * 2022/08/20 <br>
 *
 * TiDB数据库的操作实现，直接继承MysqlOperateServiceImpl，也就是说，和mysql一样
 */
@Service
public class TidbOperateServiceImpl extends MysqlOperateServiceImpl {
    
    @Override
    public DatabaseTypeEnum getSupportedDatabaseType() {
        return DatabaseTypeEnum.TIDB;
    }

    @Override
    public Integer getClusterNodeCount(DatabaseDO databaseDO) {
        // TODO tidb 是真正的分布式，可以获得集群个数，这里未实现
        return -1; // 先返回-1表示不支持
    }

    @Override
    public Map<String, Integer> getShardNum(DatabaseDO databaseDO) {
        // TODO tidb 是真正的分布式，可以获得分片数，这里未实现
        return null;
    }

}
