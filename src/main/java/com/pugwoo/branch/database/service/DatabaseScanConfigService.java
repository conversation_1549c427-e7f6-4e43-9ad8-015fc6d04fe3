package com.pugwoo.branch.database.service;

import com.pugwoo.branch.database.entity.DatabaseScanConfigDO;
import com.pugwoo.branch.database.entity.DatabaseScanConfigDetailDO;
import com.pugwoo.branch.database.entity.DatabaseScanResultDO;
import com.pugwoo.branch.database.vo.DatabaseScanConfigVO;
import com.pugwoo.dbhelper.model.PageData;

import java.util.List;

public interface DatabaseScanConfigService {

    DatabaseScanConfigVO getConfigById(Long configId);

    /**
     * 查询配置列表
     */
    PageData<DatabaseScanConfigVO> getPage(int page, int pageSize);

    /**
     * 新增或更新配置
     */
    Long insertOrUpdate(DatabaseScanConfigDO databaseScanConfigDO);

    /**
     * 查询配置详情
     */
    List<DatabaseScanConfigDetailDO> getConfigDetails(Long configId);

    /**
     * 替换配置详情，这里采取全部删除再插入的方式，比较简单，后续再优化
     */
    void replaceScanDetails(Long configId, List<DatabaseScanConfigDetailDO> scanDetails);

    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     */
    PageData<DatabaseScanResultDO> getScanResultPage(long databaseScanConfigId, int page, int pageSize);

}
