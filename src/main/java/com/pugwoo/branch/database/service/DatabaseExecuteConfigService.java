package com.pugwoo.branch.database.service;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.database.entity.DatabaseExecuteConfigDO;
import com.pugwoo.dbhelper.model.PageData;

import java.util.Map;

public interface DatabaseExecuteConfigService {

    /**
     * 通过主键获得数据
     */
    DatabaseExecuteConfigDO getById(Long id);
    
    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     * @param name 任务名称，可选过滤条件
     * @param status 状态，可选过滤条件
     * @param databaseId 数据库ID，可选过滤条件
     */
    PageData<DatabaseExecuteConfigDO> getPage(int page, int pageSize, String name, String status, Long databaseId);
    
    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdate(DatabaseExecuteConfigDO databaseExecuteConfigDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

    /**
     * 执行任务
     */
    void execute(Long id);

    /**
     * 手动启动执行任务
     */
    void startExecute(Long id);

    /**
     * 停止执行任务
     */
    void stopExecute(Long id);

    /**
     * 获取执行进度
     */
    Map<String, Object> getExecutionProgress(Long id);

}