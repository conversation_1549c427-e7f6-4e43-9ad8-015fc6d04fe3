package com.pugwoo.branch.database.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.branch.database.entity.DatabaseExecuteResultDO;
import com.pugwoo.branch.database.service.DatabaseExecuteResultService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DatabaseExecuteResultServiceImpl implements DatabaseExecuteResultService {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public DatabaseExecuteResultDO getById(Long id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(DatabaseExecuteResultDO.class, id);
    }

    @Override
    public PageData<DatabaseExecuteResultDO> getPage(int page, int pageSize, Long taskId) {
        WhereSQL where = new WhereSQL();
        if (taskId != null) {
            where.and("task_id=?", taskId);
        }

        where.addOrderBy("id desc");
        return dbHelper.getPage(DatabaseExecuteResultDO.class, page, pageSize,
                where.getSQL(), where.getParams());
    }

    @Override
    public ResultBean<Long> insertOrUpdate(DatabaseExecuteResultDO databaseExecuteResultDO) {
        if(databaseExecuteResultDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(databaseExecuteResultDO);
        return rows > 0 ? ResultBean.ok(databaseExecuteResultDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        DatabaseExecuteResultDO databaseExecuteResultDO = new DatabaseExecuteResultDO();
        databaseExecuteResultDO.setId(id);
        return dbHelper.delete(databaseExecuteResultDO) > 0;
    }

}