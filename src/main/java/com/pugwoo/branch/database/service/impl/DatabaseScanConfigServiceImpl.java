package com.pugwoo.branch.database.service.impl;

import com.pugwoo.branch.common.BranchException;
import com.pugwoo.branch.database.entity.DatabaseScanConfigDO;
import com.pugwoo.branch.database.entity.DatabaseScanConfigDetailDO;
import com.pugwoo.branch.database.entity.DatabaseScanResultDO;
import com.pugwoo.branch.database.service.DatabaseScanConfigService;
import com.pugwoo.branch.database.vo.DatabaseScanConfigVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DatabaseScanConfigServiceImpl implements DatabaseScanConfigService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Override
    public DatabaseScanConfigVO getConfigById(Long configId) {
        DatabaseScanConfigVO d = dbHelper.getByKey(DatabaseScanConfigVO.class, configId);
        if (d == null) {
            throw new BranchException("扫描配置id:" + configId + "不存在");
        }
        return d;
    }

    @Override
    public PageData<DatabaseScanConfigVO> getPage(int page, int pageSize) {
        return dbHelper.getPage(DatabaseScanConfigVO.class, page, pageSize);
    }

    @Override
    public Long insertOrUpdate(DatabaseScanConfigDO databaseScanConfigDO) {
        if(databaseScanConfigDO == null) {
            throw new BranchException("缺少参数");
        }

        int rows = dbHelper.insertOrUpdate(databaseScanConfigDO);
        if (rows != 1) {
            throw new BranchException("新增或更新失败");
        }
        return databaseScanConfigDO.getId();
    }

    @Override
    public List<DatabaseScanConfigDetailDO> getConfigDetails(Long configId) {
        return dbHelper.getAll(DatabaseScanConfigDetailDO.class, "where scan_config_id=?", configId);
    }

    @Override
    public void replaceScanDetails(Long configId, List<DatabaseScanConfigDetailDO> scanDetails) {
        if (configId == null || scanDetails == null) {
            throw new BranchException("缺少参数");
        }
        dbHelper.delete(DatabaseScanConfigDetailDO.class, "where scan_config_id=?", configId);
        dbHelper.insertBatchWithoutReturnId(scanDetails);
    }

    @Override
    public PageData<DatabaseScanResultDO> getScanResultPage(long databaseScanConfigId, int page, int pageSize) {
        return dbHelper.getPage(DatabaseScanResultDO.class, page, pageSize, "where scan_config_id=? order by id desc", databaseScanConfigId);
    }
}
