package com.pugwoo.branch.database.service;

import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.model.DatabaseStatusDTO;
import com.pugwoo.branch.database.vo.DatabaseVO;
import com.pugwoo.dbhelper.model.PageData;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <br>
 * 2022/08/20 <br>
 * 负责数据库信息的增删改查
 */
public interface DatabaseService {

    /**
     * 查询数据库信息，不存在抛异常
     */
    DatabaseDO getById(Long databaseId) throws AdminInnerException;

    /**
     * 获取数据库信息 分页
     */
    PageData<DatabaseVO> getPage(int page, int pageSize, String env, String type);

    /**
     * 获得数据库状态
     */
    DatabaseStatusDTO getDatabaseStatus(Long databaseId);

    /**
     * 获取数据库信息
     * @param databaseIds ids
     * @return 数据库信息
     */
    List<DatabaseDO> getByIds(Collection<Long> databaseIds);

    /**
     * 获得全部数据库实例
     */
    List<DatabaseDO> getAll();

    /**
     * 获取开启了容量监控的数据库实例
     */
    List<DatabaseDO> getCapacityMonitorDatabase();

    /**
     * 新增or更新数据信息
     * @param databaseDO 数据库信息
     */
    void insertOrUpdate(DatabaseDO databaseDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

    /**
     * 获得对应数据的的操作服务
     */
    DatabaseOperateService getDatabaseOperateService(DatabaseDO databaseDO);

    /**
     * 执行查询
     * @param databaseDO  连接信息
     * @param sql 执行的sql
     * @return 执行结果
     */
    List<Map<String, Object>> executeQuery(DatabaseDO databaseDO, String databaseName, String sql, Object... args);

    /**
     * 执行修改操作（INSERT, UPDATE, DELETE等）
     * @param databaseDO 连接信息
     * @param databaseName 数据库名称
     * @param sql 执行的sql
     * @param args 参数
     * @return 影响的行数
     */
    int executeModify(DatabaseDO databaseDO, String databaseName, String sql, Object... args);

    /**
     * 是否开启内置监控
     */
    boolean enableMonitor(Long databaseId, Boolean enabled);

    /**
     * 开始容量监控
     */
    boolean enableCapacityMonitor(Long databaseId, Boolean enabled);

    int getBuildInMonitorCount(Long databaseId);

    /**
     * 重置连接池，主要是当数据库修改权限时，可以直接重置老的连接
     */
    void resetConnectionPool();

}
