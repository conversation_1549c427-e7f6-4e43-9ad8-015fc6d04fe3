package com.pugwoo.branch.database.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.branch.database.entity.DatabaseCheckConfigDO;
import com.pugwoo.branch.database.entity.DatabaseCheckResultDO;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.service.DatabaseCheckConfigService;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.branch.database.utils.BranchSQLUtils;
import com.pugwoo.branch.database.vo.DatabaseCheckConfigVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.mvel2.MVEL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

@Service
public class DatabaseCheckConfigServiceImpl implements DatabaseCheckConfigService {

    @Autowired
    private DBHelper dbHelper;
    @Autowired
    private DatabaseService databaseService;
    @Autowired
    private AdminNotifyService notifyService;

    /**缓存用：用于控制成功时插入的频率，不要插入太多*/
    private final Map<Long, Date> lastInsertSuccessTime = new HashMap<>();

    @Override
    public PageData<DatabaseCheckConfigVO> getPage(int page, int pageSize) {
        return dbHelper.getPage(DatabaseCheckConfigVO.class, page, pageSize, "order by is_last_success,id");
    }

    @Override
    public ResultBean<Long> insertOrUpdate(DatabaseCheckConfigDO databaseCheckConfigDO) {
        if(databaseCheckConfigDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }

        if (databaseCheckConfigDO.getId() == null) {
            databaseCheckConfigDO.setIsEnabled(true); // 新增默认启用
        }

        int rows = dbHelper.insertOrUpdate(databaseCheckConfigDO);
        return rows > 0 ? ResultBean.ok(databaseCheckConfigDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }
        DatabaseCheckConfigDO databaseCheckConfigDO = new DatabaseCheckConfigDO();
        databaseCheckConfigDO.setId(id);
        return dbHelper.delete(databaseCheckConfigDO) > 0;
    }
    
    @Override
    public List<DatabaseCheckConfigDO> getAllEnabledList() {
        return dbHelper.getAll(DatabaseCheckConfigDO.class, "where is_enabled=1");
    }

    @Override
    public DatabaseCheckResultDO executeAndAssertSql(DatabaseDO databaseDO, String databaseName,
                                                     String sql, String assertion, String detailSql) {

        // 替换sql的内置变量
        if (StringTools.isNotBlank(sql)) {
            sql = sql.replace("${TODAY}", DateUtils.formatDate(LocalDate.now()));
            sql = sql.replace("${YESTERDAY}", DateUtils.formatDate(LocalDate.now().minusDays(1)));
        }
        if (StringTools.isNotBlank(detailSql)) {
            detailSql = detailSql.replace("${TODAY}", DateUtils.formatDate(LocalDate.now()));
            detailSql = detailSql.replace("${YESTERDAY}", DateUtils.formatDate(LocalDate.now().minusDays(1)));
        }

        DatabaseCheckResultDO databaseCheckResultDO = new DatabaseCheckResultDO();
        databaseCheckResultDO.setSql(sql);
        databaseCheckResultDO.setAssertion(assertion);
        databaseCheckResultDO.setIsSendNotify(false); // 初始化为未发送告警

        if (databaseDO == null) {
            databaseCheckResultDO.setIsSuccess(false);
            databaseCheckResultDO.setErrorMsg("未能找到数据库连接信息");
            return databaseCheckResultDO;
        }

        // 查询数据
        Map<String, Object> vars = new HashMap<>(); // 给断言用

        List<Map<String, Object>> sqlResult;
        try {
            List<String> sqlList = BranchSQLUtils.splitSql(sql);
            long start = System.currentTimeMillis();
            if (sqlList.size() == 1) {
                sqlResult = databaseService.executeQuery(databaseDO, databaseName, sql);
                vars.put("rows", sqlResult);
            } else if (sqlList.size() > 1) {
                for (int i = 0; i < sqlList.size(); i++) {
                    String sqlItem = sqlList.get(i);
                    List<Map<String, Object>> sqlItemResult = databaseService.executeQuery(databaseDO, databaseName, sqlItem);
                    vars.put("rows" + (i == 0 ? "" : (i + 1)), sqlItemResult);
                }
            }
            databaseCheckResultDO.setSqlRowsJson(JSON.toJson(vars));
            databaseCheckResultDO.setSqlTimeMs((int) (System.currentTimeMillis() - start));
        } catch (Exception e) {
            databaseCheckResultDO.setIsSuccess(false);
            databaseCheckResultDO.setErrorMsg("查询数据库错误: " + e.getMessage() + ",数据库ID:" + databaseDO.getId());
            return databaseCheckResultDO;
        }

        // 断言
        try {
            Object result = MVEL.eval(assertion.trim(), vars);
            if (result instanceof Boolean) {
                databaseCheckResultDO.setIsSuccess((boolean) result);
                databaseCheckResultDO.setErrorMsg("断言结果: " + JSON.toJson(result));
            } else {
                databaseCheckResultDO.setIsSuccess(false);
                databaseCheckResultDO.setErrorMsg("断言结果不是boolean: " + JSON.toJson(result));
            }
        } catch (RuntimeException e) {
            databaseCheckResultDO.setIsSuccess(false);
            databaseCheckResultDO.setErrorMsg("断言发生异常: " + e.getMessage());
        }

        if (!databaseCheckResultDO.getIsSuccess()) { // 断言失败的情况下
            if (StringTools.isNotBlank(detailSql)) {
                databaseCheckResultDO.setDetailSql(detailSql);
                List<String> detailSqlList = BranchSQLUtils.splitSql(detailSql);

                try {
                    if (detailSqlList.size() == 1) {
                        List<Map<String, Object>> detailResult = databaseService.executeQuery(databaseDO, databaseName, detailSql);
                        databaseCheckResultDO.setDetailRowsJson(JSON.toJson(detailResult));
                    } else if (detailSqlList.size() >1) {
                        List<Object> result = new ArrayList<>();
                        for (String detailSqlItem : detailSqlList) {
                            List<Map<String, Object>> detailResult = databaseService.executeQuery(databaseDO, databaseName, detailSqlItem);
                            result.add(detailResult);
                        }
                        databaseCheckResultDO.setDetailRowsJson(JSON.toJson(result));
                    }
                } catch (Exception e) {
                    databaseCheckResultDO.setErrorMsg(databaseCheckResultDO.getErrorMsg() + ",查询明细错误: " + e.getMessage());
                }
            }
        }

        return databaseCheckResultDO;
    }

    @Override
    @Transactional
    public void updateCheckResult(DatabaseCheckResultDO databaseCheckResultDO, DatabaseCheckConfigDO databaseCheckConfigDO) {
        if(databaseCheckResultDO == null) {
            return;
        }
        boolean isSuccess = databaseCheckResultDO.getIsSuccess();
        if (isSuccess && databaseCheckConfigDO.getSuccLogRateSecs() != null) {
            boolean needInsert = true;
            Date lastTime = lastInsertSuccessTime.get(databaseCheckConfigDO.getId());
            if (lastTime != null && System.currentTimeMillis() - lastTime.getTime() < databaseCheckConfigDO.getSuccLogRateSecs() * 1000) {
                needInsert = false; // 本机插入过，可以肯定不需要插入
            } else {
                // 查询db看是否超过成功插入间隔时间（这个是为了控制成功插入的量）
                DatabaseCheckResultDO one = dbHelper.getOne(DatabaseCheckResultDO.class,
                        "where database_check_config_id=? order by id", databaseCheckConfigDO.getId());
                if (one != null && one.getIsSuccess() != null && one.getIsSuccess() && one.getCreateTime() != null
                    && System.currentTimeMillis() - one.getCreateTime().getTime() < databaseCheckConfigDO.getSuccLogRateSecs() * 1000) {
                    needInsert = false;
                }
            }

            if (needInsert) {
                lastInsertSuccessTime.put(databaseCheckConfigDO.getId(), new Date());
                dbHelper.insert(databaseCheckResultDO);
            }
        } else {
            if (isSuccess) {
                lastInsertSuccessTime.put(databaseCheckConfigDO.getId(), new Date());
            }
            dbHelper.insert(databaseCheckResultDO);
        }

        // 仅更新config的统计信息
        Date now = new Date();
        DatabaseCheckConfigDO checkConfigDO = new DatabaseCheckConfigDO();
        checkConfigDO.setId(databaseCheckResultDO.getDatabaseCheckConfigId());
        checkConfigDO.setIsLastSuccess(isSuccess);
        checkConfigDO.setLastTime(now);
        String countSetSql = "count_success = count_success + 1";
        if (!isSuccess) {
            checkConfigDO.setLastErrorTime(now);
            countSetSql = "count_error = count_error + 1";
        }
        dbHelper.update(checkConfigDO);
        dbHelper.updateCustom(checkConfigDO, countSetSql);
    }

    @Override
    public List<DatabaseCheckResultDO> getLatestCheckItems(Long checkConfigId, int limit) {
        return dbHelper.getAll(DatabaseCheckResultDO.class,
                "where database_check_config_id=? order by id desc limit ?", checkConfigId, limit);
    }

    @Override
    public void resetFailCount(Long id) {
        if(id == null) {
            return;
        }
        DatabaseCheckConfigDO checkConfigDO = new DatabaseCheckConfigDO();
        checkConfigDO.setId(id);
        checkConfigDO.setCountError(0);
        dbHelper.update(checkConfigDO);
    }

    @Override
    public void sendEmail(String sendEmails) {
        notifyService.sendEmail(sendEmails, "仅测试发送用",
                "测试发送,hello," + DateUtils.format(new Date()));
    }
}
