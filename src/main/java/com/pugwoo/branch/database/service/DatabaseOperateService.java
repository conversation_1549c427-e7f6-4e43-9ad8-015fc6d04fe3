package com.pugwoo.branch.database.service;

import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.enums.DatabaseTypeEnum;
import com.pugwoo.branch.database.model.*;
import com.pugwoo.dbhelper.DBHelper;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR> <br>
 * 2022/08/20 <br>
 *
 * 数据库操作接口，每个数据库类型都有一个数据操作实现
 */
public interface DatabaseOperateService {

    /**
     * 获取该实现对应的数据库类型
     * @return 数据库操作类型枚举
     */
    DatabaseTypeEnum getSupportedDatabaseType();

    /**
     * 返回数据库名称的转义字符
     */
    String getEscapeChar();

    /**
     * 获得数据库连接实例。
     * 当databaseDO还没有存入数据库之前（例如test connection功能），也即还没有databaseId之前，是每次都拿一个新的DataSource；
     * 当databaseDO已经存入数据库之后，也即有了databaseId之后，就会走单例逻辑，保证每个数据库只有一个DataSource
     *
     * @param databaseDO 注意，这里之所以要传入databaseDO，是因为目前支持在DatabaseDO存入数据库之前，就可以连接数据库
     * @param isTmp 是否是临时的，如果是临时的，则不放入连接池；注意，这个参数必须的，就算是databaseDO有id，也有临时测试的时候
     */
    DataSource getDataSource(DatabaseDO databaseDO, boolean isTmp);

    /**
     * 获得一个只会一直使用同一个数据库连接的JdbcTemplate。
     * 【重要!】：这里拿到的jdbcTemplate用完之后记得自行close：jdbcTemplate.getDataSource().getConnection().close()
     */
    JdbcTemplate getJdbcTemplateWithOneConnection(DatabaseDO databaseDO);

    /**
     * 当修改了数据库信息时，需要清空对应的DataSource和jdbcTemplate
     */
    void clearDatabaseConnection(Long databaseId);

    /**
     * 重置所有的数据库连接池
     */
    void resetAllDatabaseConnection();

    /**
     * 检查连接状态，状态将填充在入参DTO中
     * @param databaseDO 要测试的数据库
     * @param isTest 是否是测试用的，如果测试用的，就不用连接池里保存的连接
     * @return ping探测的状态
     */
    DatabaseStatusDTO ping(DatabaseDO databaseDO, boolean isTest);

    /**
     * 列出所有数据库名
     */
    List<String> listDatabaseNames(DatabaseDO databaseDO);

    /**
     * 执行查询
     * @param databaseDO  连接信息
     * @param sql 执行的sql
     * @return 执行结果
     */
    List<Map<String, Object>> executeQuery(DatabaseDO databaseDO, String databaseName, String sql, Object... args);

    /**
     * 执行修改操作（INSERT, UPDATE, DELETE等）
     * @param databaseDO 连接信息
     * @param databaseName 数据库名称
     * @param sql 执行的sql
     * @param args 参数
     * @return 影响的行数
     */
    int executeModify(DatabaseDO databaseDO, String databaseName, String sql, Object... args);

    /**
     * 使用dbhelper执行查询
     */
    <R> R executeQuery(DatabaseDO databaseDO, Function<DBHelper, R> function);

    /**
     * 使用dbhelper执行查询
     */
    <R> R executeQuery(DatabaseDO databaseDO, String databaseName, Function<DBHelper, R> function);

    /**
     * 查询全部表的物理占用大小
     */
    List<TableSizeInfoDTO> getAllTableFileSize(DatabaseDO databaseDO);

    /**
     * 查询数据库实例的节点个数
     */
    Integer getClusterNodeCount(DatabaseDO databaseDO);

    /**
     * 查询数据库的分片数，支持集群名称 -> 分片数
     */
    Map<String, Integer> getShardNum(DatabaseDO databaseDO);

    /**
     * 查询总行数
     */
    Long getTotalRows(DatabaseDO databaseDO, String databaseName, String sql);

    /**
     * 自动group sql里的每一列
     * @param specificColumn 如果有值，则按指定的列来，其他列不查询了
     * @param aggFunc 聚合函数，默认是count(*)
     */
    List<GroupDataDTO> viewGroupData(DatabaseDO databaseDO, String databaseName, String sql,
                                     int topCount, int bottomCount, String specificColumn,
                                     String aggFunc);

    /**
     * 列出表的列信息
     * @param databaseName 如果为空则不切换
     */
    List<ColumnInfoDTO> listColumnInfo(DatabaseDO databaseDO, String databaseName, String tableName);

    /**
     * 查询锁信息
     */
    ProcessListRespDTO queryProcessList(DatabaseDO databaseDO, ProcessListReqDTO config);

    /**
     * 杀死指定线程
     */
    boolean killProcess(DatabaseDO databaseDO, Long threadId);

    /**
     * 查询系统主要的变量及值，这里只返回部分关心的，后续逐个加
     * 说明：这个目前是只给内部的一些内置的判断用的，历史原因，这个先做了，就先放在这里了
     */
    Map<String, String> queryDBVariables(DatabaseDO databaseDO);

    /**
     * 查询数据库的系统变量
     */
    List<SystemVariablesDTO> querySystemVariables(DatabaseDO databaseDO);

}
