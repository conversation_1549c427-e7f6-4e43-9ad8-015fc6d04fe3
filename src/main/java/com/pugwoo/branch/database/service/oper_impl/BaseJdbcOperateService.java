package com.pugwoo.branch.database.service.oper_impl;

import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.model.ColumnInfoDTO;
import com.pugwoo.branch.database.model.DatabaseStatusDTO;
import com.pugwoo.branch.database.model.GroupDataDTO;
import com.pugwoo.branch.database.service.DatabaseOperateService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.SneakyThrows;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.PlainSelect;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 各种数据库的基类操作，这里有一些规范限制：
 * 1）由基类保证了每个数据库的DataSource实例是单例
 *
 * <AUTHOR>
 * 2022/09/04
 */
public abstract class BaseJdbcOperateService implements DatabaseOperateService {

    /**
     * 存放各数据库的数据库连接实例，由此保证DataSource是单例的<br>
     * 注意：当数据库的信息发生变化时，需要清空对应的DataSource
     */
    private static final Map<Long, DataSource> dataSourceMap = new ConcurrentHashMap<>();

    /**
     * 获取数据源，由子类实现，使用者不要调这个方法
     * @param databaseDO 连接信息
     * @return 数据源
     */
    abstract protected DataSource getDataSourceForImplement(DatabaseDO databaseDO);

    /**
     * 获取指定表的列
     *
     * @param jdbcTemplate 不会自动关闭，请调用者自行关闭
     */
    abstract protected List<ColumnInfoDTO> listColumnInfo(JdbcTemplate jdbcTemplate, String databaseName, String tableName);

    @Override
    public DataSource getDataSource(DatabaseDO databaseDO, boolean isTmp) {
        DataSource dataSource = getDataSourceForImplement(databaseDO);
        if (isTmp || databaseDO.getId() == null) { // 临时的不创建连接池
            return dataSource;
        }

        return dataSourceMap.computeIfAbsent(databaseDO.getId(), k -> {
            HikariConfig config = new HikariConfig();
            config.setDataSource(dataSource);
            config.setMaximumPoolSize(10);

            return new HikariDataSource(config);
        });
    }

    @SneakyThrows
    @Override
    public JdbcTemplate getJdbcTemplateWithOneConnection(DatabaseDO databaseDO) {
        DataSource dataSource = getDataSource(databaseDO, false);
        Connection connection = dataSource.getConnection();
        return new JdbcTemplate(new SingleConnectionDataSource(connection, false));
    }

    @Override
    public void clearDatabaseConnection(Long databaseId) {
        DataSource dataSource = dataSourceMap.remove(databaseId);
        if (dataSource instanceof HikariDataSource) {
            ((HikariDataSource) dataSource).close();
        }
    }

    @Override
    public void resetAllDatabaseConnection() {
        for (DataSource dataSource : dataSourceMap.values()) {
            if (dataSource instanceof HikariDataSource) {
                try {
                    ((HikariDataSource) dataSource).close();
                } catch (Exception ignored) {
                }
            }
        }
        dataSourceMap.clear();
    }

    @Override
    public DatabaseStatusDTO ping(DatabaseDO databaseDO, boolean isTest) {
        DataSource dataSource = getDataSource(databaseDO, isTest);
        long start = System.currentTimeMillis();
        DatabaseStatusDTO databaseStatusDTO = new DatabaseStatusDTO();
        try (Connection ignored = dataSource.getConnection()) {
            long end = System.currentTimeMillis();
            databaseStatusDTO.connectSuccess((int)(end - start));
        } catch (SQLException e) {
            databaseStatusDTO.connectFail(e.getErrorCode(), e.getMessage());
        }
        return databaseStatusDTO;
    }

    @SneakyThrows
    @Override
    public List<Map<String, Object>> executeQuery(DatabaseDO databaseDO, String databaseName, String sql, Object ...args) {
        JdbcTemplate jdbcTemplate = getJdbcTemplateWithOneConnection(databaseDO);
        if (StringTools.isNotBlank(databaseName)) {
            jdbcTemplate.execute("use " + databaseName);
        }
        try {
            return jdbcTemplate.queryForList(sql, args);
        } finally {
            close(jdbcTemplate);
        }
    }

    @SneakyThrows
    @Override
    public int executeModify(DatabaseDO databaseDO, String databaseName, String sql, Object ...args) {
        JdbcTemplate jdbcTemplate = getJdbcTemplateWithOneConnection(databaseDO);
        if (StringTools.isNotBlank(databaseName)) {
            jdbcTemplate.execute("use " + databaseName);
        }
        try {
            return jdbcTemplate.update(sql, args);
        } finally {
            close(jdbcTemplate);
        }
    }

    @Override
    public <R> R executeQuery(DatabaseDO databaseDO, Function<DBHelper, R> function) {
        return executeQuery(databaseDO, null, function);
    }

    @SneakyThrows
    @Override
    public <R> R executeQuery(DatabaseDO databaseDO, String databaseName, Function<DBHelper, R> function) {
        DataSource dataSource = getDataSource(databaseDO, false);
        Connection connection = dataSource.getConnection();

        // 因为dbHelper中有一个动作会关闭连接，所以这里设置suppressClose为true，不让关闭
        JdbcTemplate jdbcTemplate = new JdbcTemplate(new SingleConnectionDataSource(connection, true));
        if (StringTools.isNotBlank(databaseName)) {
            jdbcTemplate.execute("use " + databaseName);
        }

        DBHelper dbHelper = new SpringJdbcDBHelper(jdbcTemplate);
        try {
            return function.apply(dbHelper);
        } finally {
            close(connection);
        }
    }

    @Override
    public List<ColumnInfoDTO> listColumnInfo(DatabaseDO databaseDO, String databaseName, String tableName) {
        JdbcTemplate jdbcTemplate = getJdbcTemplateWithOneConnection(databaseDO);
        try {
            return listColumnInfo(jdbcTemplate, databaseName, tableName);
        } finally {
            close(jdbcTemplate);
        }
    }

    @Override
    public List<String> listDatabaseNames(DatabaseDO databaseDO) {
        List<Map<String, Object>> result = executeQuery(databaseDO, null, "show DATABASES");
        // 只需要把这一列的值取出来，不关心key叫什么
        return ListUtils.transform(result, map -> (String) map.values().iterator().next());
    }

    @Override
    @SneakyThrows
    public Long getTotalRows(DatabaseDO databaseDO, String databaseName, String sql) {
        return executeQuery(databaseDO, databaseName,
                dbHelper -> dbHelper.getRawOne(Long.class,
                        "select count(*) from (" + sql + "\n) t")); // 这里一定要\n换行，因为用户输入的sql可能最后一行
    }

    @Override
    @SneakyThrows
    public List<GroupDataDTO> viewGroupData(DatabaseDO databaseDO, String databaseName, String sql,
                                            int topCount, int bottomCount, String specificColumn,
                                            String aggFunc) {
        if (StringTools.isBlank(aggFunc)) {
            aggFunc = "count(*)";
        }

        JdbcTemplate jdbcTemplate = getJdbcTemplateWithOneConnection(databaseDO);
        if (StringTools.isNotBlank(databaseName)) {
            jdbcTemplate.execute("use " + databaseName);
        }

        String ec = getEscapeChar();

        boolean isSpecificColumn = StringTools.isNotBlank(specificColumn);
        try {
            // 1. 确定查询的列
            List<String> columnList = new ArrayList<>();
            if (isSpecificColumn) {
                columnList.add(specificColumn);
            } else {
                // 先取出一条数据，看看有哪些列
                String getOneSql = "select * from (" + sql + "\n) t limit 1"; // 这里一定要\n换行，因为用户输入的sql可能最后一行，以下同理
                List<Map<String, Object>> one = jdbcTemplate.queryForList(getOneSql);
                if (one.isEmpty()) {
                    return new ArrayList<>();
                }
                columnList.addAll(one.getFirst().keySet());
            }

            List<GroupDataDTO> result = new ArrayList<>();
            for (String columnName : columnList) {
                GroupDataDTO g = new GroupDataDTO();
                g.setColumnName(columnName);

                // top value count
                String topCountSql = "select " + ec + columnName.trim() + ec + "," + aggFunc + " as count from (" + sql + "\n) t group by " + ec
                        + columnName.trim() + ec + " order by count desc limit " + topCount;
                List<Map<String, Object>> topValueCountResult = jdbcTemplate.queryForList(topCountSql);
                g.setTopValueAndCount(ListUtils.transform(topValueCountResult, o -> {
                    GroupDataDTO.ValueAndCount valueAndCount = new GroupDataDTO.ValueAndCount();
                    valueAndCount.setValue(trim(o.get(columnName)));
                    valueAndCount.setCount(o.get("count"));
                    return valueAndCount;
                }));

                // 只有当全部列查询时，才查以下信息
                if (!isSpecificColumn) {
                    // distinct value count
                    String distinctCount = "select count(distinct " + ec + columnName.trim() + ec + ") from (" + sql + "\n) t";
                    List<Map<String, Object>> distinctCountResult = jdbcTemplate.queryForList(distinctCount);
                    g.setDistinctValueCount(NumberUtils.parseInt(distinctCountResult.get(0).values().iterator().next()));

                    // bottom value count，当top的个数还没有达到topValueCount时，就不需要再查bottom了
                    if (topValueCountResult.size() < topCount) {
                        g.setBottomValueAndCount(new ArrayList<>());
                    } else {
                        String bottomCountSql = "select " + ec + columnName.trim() + ec + "," + aggFunc + " as count from (" + sql + "\n) t group by " + ec
                                + columnName.trim() + ec + " order by count asc limit " + bottomCount;
                        List<Map<String, Object>> bottomValueCountResult = jdbcTemplate.queryForList(bottomCountSql);
                        g.setBottomValueAndCount(ListUtils.transform(bottomValueCountResult, o -> {
                            GroupDataDTO.ValueAndCount valueAndCount = new GroupDataDTO.ValueAndCount();
                            valueAndCount.setValue(trim(o.get(columnName)));
                            valueAndCount.setCount(o.get("count"));
                            return valueAndCount;
                        }));
                        ListUtils.sortDescNullLast(g.getBottomValueAndCount(), o -> {
                            Object count = o.getCount();
                            if (count instanceof Comparable<?>) {
                                return (Comparable<?>) count;
                            } else {
                                return count == null ? null : count.toString();
                            }
                        });
                    }

                    // max value
                    String maxValueSql = "select max(" + ec + columnName.trim() + ec + ") from (" + sql + "\n) t";
                    List<Map<String, Object>> maxValueResult = jdbcTemplate.queryForList(maxValueSql);
                    g.setMaxValue(trim(maxValueResult.get(0).values().iterator().next()));

                    // min value
                    String minValueSql = "select min(" + ec + columnName.trim() + ec + ") from (" + sql + "\n) t";
                    List<Map<String, Object>> minValueResult = jdbcTemplate.queryForList(minValueSql);
                    g.setMinValue(trim(minValueResult.get(0).values().iterator().next()));
                }

                result.add(g);
            }

            if (!isSpecificColumn) {
                fillColumnComment(sql, result, jdbcTemplate);
            }

            return result;
        } finally {
            close(jdbcTemplate);
        }
    }

    private void fillColumnComment(String sql, List<GroupDataDTO> groupDataList, JdbcTemplate jdbcTemplate) {
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof PlainSelect plainSelect) {
                FromItem fromItem = plainSelect.getFromItem();
                if (fromItem instanceof Table table) {
                    String tableName = table.getFullyQualifiedName();
                    List<ColumnInfoDTO> columnInfoDTOS = listColumnInfo(jdbcTemplate, null, tableName);
                    Map<String, String> commentMap = ListUtils.toMap(columnInfoDTOS, o -> o.getColumnName(), o -> o.getColumnComment());
                    groupDataList.forEach(o -> {
                        o.setColumnComment(commentMap.getOrDefault(o.getColumnName(), ""));
                    });
                }
            }
        } catch (Exception ignored) {
            // 获取行注释是次要功能，可降级
        }
    }

    private Object trim(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof String s) {
            if (s.length() > 256) {
                return s.substring(0, 256) + "【截断，总长度：" + s.length() + "】";
            } else {
                return s;
            }
        } else {
            return obj;
        }
    }

    private void close(JdbcTemplate jdbcTemplate) {
        try {
            DataSource dataSource = jdbcTemplate.getDataSource();
            if (dataSource != null) {
                Connection connection = dataSource.getConnection();
                if (connection != null) {
                    connection.close();
                }
            }
        } catch (Exception ignored) {
        }
    }

    private void close(Connection connection) {
        try {
            connection.close();
        } catch (Exception ignored) {
        }
    }

}
