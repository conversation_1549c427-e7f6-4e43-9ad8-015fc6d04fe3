package com.pugwoo.branch.database.service.impl;

import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.branch.common.BranchException;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.entity.DatabaseScanConfigDetailDO;
import com.pugwoo.branch.database.entity.DatabaseScanResultDO;
import com.pugwoo.branch.database.enums.DatabaseScanTextTypeEnum;
import com.pugwoo.branch.database.service.DatabaseScanService;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.branch.database.vo.DatabaseScanConfigVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.mvel2.MVEL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class DatabaseScanServiceImpl implements DatabaseScanService {

    @Autowired @Qualifier("adminDBHelper")
    private DBHelper dbHelper;
    @Autowired
    private DatabaseService databaseService;
    @Autowired
    private AdminNotifyService notifyService;

    @Override
    public void scan() {
        // 1. 获得扫描配置，并过滤留下需要扫描的配置
        List<DatabaseScanConfigVO> all = dbHelper.getAll(DatabaseScanConfigVO.class, "where enabled=1");
        all = ListUtils.filter(all, o -> {
            if (o.getLastScanTime() == null) {
                return true;
            }
            return System.currentTimeMillis() >= o.getLastScanTime().getTime() + o.getScanIntervalSecond() * 1000;
        });

        // 2. 对于每个扫描配置，进行扫描
        for (DatabaseScanConfigVO config : all) {
            try {
                doScan(config, null, true);
            } catch (Exception e) {
                log.error("scan config id:{} fail", config.getId(), e);
                // TODO 记录异常到db
            }
        }
    }

    @Override
    public List<DatabaseScanResultDO> tryScanGroup(DatabaseScanConfigVO configDO, List<DatabaseScanConfigDetailDO> details) {
        return doScan(configDO, details, false);
    }

    /**
     * @param useSpecDetails 如果不为null，表示指定了detail，此时用这个detail而不是数据库的
     * @param isWriteDB 是否写入到db
     */
    private List<DatabaseScanResultDO> doScan(DatabaseScanConfigVO configDO,
                                              List<DatabaseScanConfigDetailDO> useSpecDetails, boolean isWriteDB) {
        List<DatabaseScanConfigDetailDO> configDetails = useSpecDetails != null ? useSpecDetails : configDO.getDetails();

        DatabaseDO databaseDO = configDO.getDatabaseDO();
        if (databaseDO == null) {
            log.error("databaseDO is null, configDO: {}", JSON.toJson(configDO));
            throw new BranchException("databaseDO is null, configId:" + configDO.getId());
        }

        // 1. 先查询出数据
        String sql = "select * from " + configDO.getDatabaseName() + "." + configDO.getTableName();
        WhereSQL whereSQL = new WhereSQL();
        if (StringTools.isNotBlank(configDO.getLastKey())) {
            whereSQL.and(configDO.getIdColumn() + " > ?", configDO.getLastKey());
        }
        List<DatabaseScanConfigDetailDO> matches = ListUtils.filter(configDetails, o ->
                DatabaseScanTextTypeEnum.MATCH.getCode().equals(o.getMatchType()) && StringTools.isNotBlank(o.getSqlCondition()));
        if (ListUtils.isNotEmpty(matches)) {
            WhereSQL matchOr = new WhereSQL();
            for (DatabaseScanConfigDetailDO match : matches) {
                matchOr.or(match.getSqlCondition());
            }
            whereSQL.and(matchOr);
        }
        List<DatabaseScanConfigDetailDO> filters = ListUtils.filter(configDetails, o ->
                DatabaseScanTextTypeEnum.FILTER.getCode().equals(o.getMatchType()) && StringTools.isNotBlank(o.getSqlCondition()));
        if (ListUtils.isNotEmpty(filters)) {
            WhereSQL filterAnd = new WhereSQL();
            for (DatabaseScanConfigDetailDO filter : filters) {
                filterAnd.and(filter.getSqlCondition());
            }
            whereSQL.and(filterAnd);
        }
        sql += whereSQL.getSQL();
        sql += " order by " + configDO.getIdColumn() + " asc limit 1000"; // 每次最多只扫描1000条，防止内存溢出

        // 2. 按分组进行扫描
        List<Map<String, Object>> list = databaseService.executeQuery(databaseDO, "", sql, whereSQL.getParams());
        if (ListUtils.isEmpty(list)) {
            configDO.setLastScanTime(new Date());
            if (isWriteDB) {
                dbHelper.update(configDO);
            }
            return new ArrayList<>();
        }

        List<DatabaseScanResultDO> result = new ArrayList<>(
                doScan(configDO.getIdColumn(), configDO.getResultMainColumns(), configDetails, list, isWriteDB,
                        configDO));

        configDO.setLastScanTime(new Date());
        Object lastKey = list.get(list.size() - 1).get(configDO.getIdColumn());
        if (lastKey == null) {
            log.error("lastKey is null, configDO: {}, sql is: {}", configDO, sql);
        } else {
            configDO.setLastKey(lastKey.toString());
        }
        if (isWriteDB) {
            dbHelper.update(configDO);
        }

        return result;
    }

    private List<DatabaseScanResultDO> doScan(String idColumn, String resultMainColumns,
                                              List<DatabaseScanConfigDetailDO> details,
                                              List<Map<String, Object>> rows,
                                              boolean isWriteDB, DatabaseScanConfigVO configDO) {
        if (ListUtils.isEmpty(details)) {
            return new ArrayList<>(); // 没有扫描规则detail，直接返回空
        }

        // 先处理match，多个match多or的关系
        List<DatabaseScanConfigDetailDO> matches = ListUtils.filter(details, o ->
                DatabaseScanTextTypeEnum.MATCH.getCode().equals(o.getMatchType()) && StringTools.isNotBlank(o.getMvelScript()));
        if (ListUtils.isNotEmpty(matches)) { // 只有不为空才处理，否则认为所有都match得到
            rows = ListUtils.filter(rows, row -> {
                Map<String, Object> rowAsMap = JSON.toMap(row);
                for (DatabaseScanConfigDetailDO match : matches) {
                    String mvelScript = match.getMvelScript();
                    Object result = MVEL.eval(mvelScript, rowAsMap);
                    if (result instanceof Boolean && (Boolean) result) {
                        return true;
                    }
                }
                return false;
            });
        }

        // 再处理filter，filter是or的关系
        List<DatabaseScanConfigDetailDO> filters = ListUtils.filter(details, o ->
                DatabaseScanTextTypeEnum.FILTER.getCode().equals(o.getMatchType()) && StringTools.isNotBlank(o.getMvelScript()));
        if (ListUtils.isNotEmpty(filters)) { // 只有不为空才处理，否则认为所有都filter得到
            rows = ListUtils.filter(rows, row -> {
                Map<String, Object> rowAsMap = JSON.toMap(row);
                for (DatabaseScanConfigDetailDO filter : filters) {
                    String mvelScript = filter.getMvelScript();
                    Object result = MVEL.eval(mvelScript, rowAsMap);
                    if (result instanceof Boolean && (Boolean) result) {
                        return false;
                    }
                }
                return true;
            });
        }

        // 将匹配到的rows存下来，按顺序
        List<String> resultMainColumn = new ArrayList<>();
        if (resultMainColumns != null) {
            String[] columns = resultMainColumns.split(",");
            for (String column : columns) {
                if (StringTools.isNotBlank(column) && !resultMainColumn.contains(column)) {
                    resultMainColumn.add(column);
                }
            }
        }

        List<DatabaseScanResultDO> results = ListUtils.transform(rows, o -> {
            DatabaseScanResultDO result = new DatabaseScanResultDO();
            result.setScanConfigId(details.get(0).getScanConfigId());
            result.setTargetTableId(o.getOrDefault(idColumn, "").toString());
            result.setTargetMainColumn(getResultColumn(resultMainColumn, o));
            return result;
        });
        if (isWriteDB) {
            // 只有需要写入db的才进行告警
            if (StringTools.isNotBlank(configDO.getSendEmail())) {
                for (DatabaseScanResultDO result : results) {
                    notifyService.sendEmail(configDO.getSendEmail(), "扫描告警:" + configDO.getName(),
                            "扫描到ID:" + result.getTargetTableId() + ",数据:" + result.getTargetMainColumn());
                }
            }
            dbHelper.insertBatchWithoutReturnId(results);
        }
        return results;
    }

    private static String getResultColumn(List<String> resultMainColumn, Map<String, Object> row) {
        if (ListUtils.isEmpty(resultMainColumn)) { // 为空表示都要
            return JSON.toJson(row);
        }
        Map<String, Object> result = new LinkedHashMap<>();
        for (String column : resultMainColumn) {
            Object value = row.get(column);
            result.put(column, value);
        }
        return JSON.toJson(result);
    }

}
