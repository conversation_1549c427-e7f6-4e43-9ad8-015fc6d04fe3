package com.pugwoo.branch.database.web.dto;

import com.pugwoo.branch.database.model.SystemVariablesDTO;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;

import java.util.List;

@Data
public class QuerySystemVariablesResp {

    private List<Item> items;

    @Data
    public static class Item {
        private String variable;

        private String variableName;

        private String defaultValue;

        private String note;

        /**
         * 实际值
         */
        private String value;

        public static QuerySystemVariablesResp.Item from(SystemVariablesDTO d) {
            QuerySystemVariablesResp.Item item = new QuerySystemVariablesResp.Item();
            item.setVariable(d.getDatabaseVariablesDO() == null ? "" : d.getDatabaseVariablesDO().getVariable());
            item.setVariableName(d.getDatabaseVariablesDO() == null ? "" : d.getDatabaseVariablesDO().getVariableName());
            item.setDefaultValue(d.getDatabaseVariablesDO() == null ? "" : d.getDatabaseVariablesDO().getDefaultValue());
            item.setNote(d.getDatabaseVariablesDO() == null ? "" : d.getDatabaseVariablesDO().getNote());
            item.setValue(d.getValue());

            if (d.getDatabaseVariablesDO() != null &&"BYTE_HUMAN".equals(d.getDatabaseVariablesDO().getCastValueMethod())) {
                item.setValue(byteHuman(item.getValue()));
            }

            return item;
        }

        private static String byteHuman(String value) {
            Long bytes = NumberUtils.parseLong(value);
            if (bytes == null) {
                return value; // 不处理
            }
            if (bytes < 1024) {
                return bytes + "B";
            } else if (bytes < 1024 * 1024) {
                return (bytes / 1024) + "KB"; // 不考虑小数点了
            } else if (bytes < 1024 * 1024 * 1024) {
                return (bytes / 1024 / 1024) + "MB"; // 不考虑小数点了
            } else {
                return (bytes / 1024 / 1024 / 1024) + "GB";
            }
        }
    }

}
