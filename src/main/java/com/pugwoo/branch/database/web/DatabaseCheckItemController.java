package com.pugwoo.branch.database.web;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.branch.database.entity.DatabaseCheckResultDO;
import com.pugwoo.branch.database.service.DatabaseCheckConfigService;
import com.pugwoo.branch.database.service.DatabaseCheckItemService;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.json.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping(value = "/database_check_item")
public class DatabaseCheckItemController {

    @Autowired
    private DatabaseCheckConfigService databaseCheckConfigService;
    @Autowired
    private DatabaseCheckItemService databaseCheckItemService;

    @GetMapping("get_page")
    public WebJsonBean<?> getPage(int page, int pageSize, Boolean isSuccess, Long databaseCheckConfigId) {
        PageData<DatabaseCheckResultDO> pageData = databaseCheckItemService.getPage(
                page, pageSize, isSuccess, databaseCheckConfigId);
        Map<String, Object> result = PageUtils.trans(pageData, o -> {
            Map<String, Object> map = JSON.toMap(o);
            String detailRowsJson = JSON.toJsonFormatted(JSON.parse(o.getDetailRowsJson()));
            if (detailRowsJson != null) {
                detailRowsJson = detailRowsJson.replace("\\n", "\n");
            }
            map.put("detailRowsJson", detailRowsJson);
            return map;
        });
        return WebJsonBean.ok(result);
    }

    /**重置失败次数*/
    @PostMapping("reset_fail_count")
    public WebJsonBean<?> resetFailCount(Long databaseCheckConfigId) {
        databaseCheckConfigService.resetFailCount(databaseCheckConfigId);
        return WebJsonBean.ok();
    }

    /**删除失败记录*/
    @PostMapping("delete_fail_record")
    public WebJsonBean<?> deleteFailRecord(Long databaseCheckConfigId) {
        databaseCheckItemService.deleteFailRecord(databaseCheckConfigId);
        return WebJsonBean.ok();
    }

}
