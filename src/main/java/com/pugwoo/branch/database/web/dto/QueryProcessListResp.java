package com.pugwoo.branch.database.web.dto;

import com.pugwoo.branch.database.model.ProcessListDTO;
import lombok.Data;

import java.util.List;

@Data
public class QueryProcessListResp {

    private List<ProcessListDTO> processList;

    /**Sleep状态的连接数*/
    private Integer sleepAndNoLockConnections;

    /**mysql专用，检查performance schema记录是否开启*/
    private Boolean isPerformanceSchemaOn;

    /**查询锁的耗时*/
    private long queryLockCostMs;

}
