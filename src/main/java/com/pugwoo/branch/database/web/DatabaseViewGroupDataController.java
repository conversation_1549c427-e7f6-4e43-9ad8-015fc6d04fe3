package com.pugwoo.branch.database.web;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.model.GroupDataDTO;
import com.pugwoo.branch.database.service.DatabaseOperateService;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.branch.database.web.dto.ViewGroupDataReq;
import com.pugwoo.branch.database.web.dto.ViewGroupDataResp;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * 负责数据库表的group查询数据
 */
@RestController
@Permission(value = "DatabaseManage", name = "数据库管理列表")
@RequestMapping("/database_view_group_data")
public class DatabaseViewGroupDataController {

    @Resource
    private DatabaseService databaseService;

    @GetMapping("/")
    public ModelAndView index() {
        return new ModelAndView("database/group_data/view");
    }

    @PostMapping("/view_group_data")
    public WebJsonBean<ViewGroupDataResp> viewGroupData(@RequestBody @Valid ViewGroupDataReq req) {
        preHandleSql(req);

        DatabaseDO databaseDO = databaseService.getById(req.getDatabaseId());
        DatabaseOperateService databaseOperateService = databaseService.getDatabaseOperateService(databaseDO);
        Long totalRows = databaseOperateService.getTotalRows(databaseDO, req.getDatabaseName(), req.getQuerySql());
        List<GroupDataDTO> groupDataDTOS =
                databaseOperateService.viewGroupData(databaseDO, req.getDatabaseName(), req.getQuerySql(),
                        20, 20, "", req.getAggFunc());

        ViewGroupDataResp resp = new ViewGroupDataResp();
        resp.setTotalRows(totalRows);
        resp.setGroupDataList(groupDataDTOS);
        return WebJsonBean.ok(resp);
    }


    @PostMapping("/view_group_data_for_specific_column")
    public ViewGroupDataResp viewGroupDataForSpecificColumn(@RequestBody @Valid ViewGroupDataReq req) {
        preHandleSql(req);

        DatabaseDO databaseDO = databaseService.getById(req.getDatabaseId());
        DatabaseOperateService databaseOperateService = databaseService.getDatabaseOperateService(databaseDO);
        List<GroupDataDTO> groupDataDTOS =
                databaseOperateService.viewGroupData(databaseDO, req.getDatabaseName(), req.getQuerySql(),
                        1000, 0, req.getSpecificColumn(), req.getAggFunc());

        ViewGroupDataResp resp = new ViewGroupDataResp();
        resp.setGroupDataList(groupDataDTOS);
        return resp;
    }

    private void preHandleSql(ViewGroupDataReq req) {
        // 特别处理一下sql后面的; 有时候复制过来的sql会带有;
        req.setQuerySql(req.getQuerySql().trim());
        if (req.getQuerySql().endsWith(";")) {
            req.setQuerySql(req.getQuerySql().substring(0, req.getQuerySql().length() - 1));
        }
    }

}
