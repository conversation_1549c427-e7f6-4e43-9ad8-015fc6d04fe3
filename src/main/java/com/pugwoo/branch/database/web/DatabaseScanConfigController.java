package com.pugwoo.branch.database.web;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.branch.database.entity.DatabaseScanConfigDO;
import com.pugwoo.branch.database.entity.DatabaseScanConfigDetailDO;
import com.pugwoo.branch.database.entity.DatabaseScanResultDO;
import com.pugwoo.branch.database.service.DatabaseScanConfigService;
import com.pugwoo.branch.database.service.DatabaseScanService;
import com.pugwoo.branch.database.vo.DatabaseScanConfigVO;
import com.pugwoo.branch.database.web.dto.QueryScanConfigDetailResp;
import com.pugwoo.branch.database.web.dto.SaveScanConfigDetailReq;
import com.pugwoo.branch.database.web.dto.TryScanReq;
import com.pugwoo.dbhelper.model.PageData;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/database_scan_config")
public class DatabaseScanConfigController {

    @Autowired
    private DatabaseScanConfigService databaseScanConfigService;
    @Autowired
    private DatabaseScanService databaseScanService;

    @GetMapping("list")
    public ModelAndView list() {
        return new ModelAndView("database/data_scan/data_scan_config_list");
    }

    @GetMapping("get_page")
    public PageData<DatabaseScanConfigVO> getPage(int page, int pageSize) {
        return databaseScanConfigService.getPage(page, pageSize);
    }

    @PostMapping("enable_disable_config")
    public WebJsonBean<?> enableDisableConfig(Long configId, Boolean enabled) {
        WebCheckUtils.assertNotNull(configId, "缺少检查ID");
        WebCheckUtils.assertNotNull(enabled, "缺少开启参数");
        DatabaseScanConfigDO configDO = new DatabaseScanConfigDO();
        configDO.setId(configId);
        configDO.setEnabled(enabled);
        databaseScanConfigService.insertOrUpdate(configDO);
        return WebJsonBean.ok();
    }

    @GetMapping("query_scan_config_detail")
    public QueryScanConfigDetailResp queryScanConfigDetail(Long configId) {
        WebCheckUtils.assertNotNull(configId, "缺少检查ID");
        List<DatabaseScanConfigDetailDO> configDetails = databaseScanConfigService.getConfigDetails(configId);
        return QueryScanConfigDetailResp.transFrom(configDetails);
    }

    @ResponseBody @PostMapping("add_or_update")
    public WebJsonBean addOrUpdate(DatabaseScanConfigDO databaseScanConfigDO) {
        WebCheckUtils.assertNotNull(databaseScanConfigDO, "缺少修改的对象参数");

        databaseScanConfigDO.setLastScanTime(null); // 不允许前端修改上次扫描时间

        Long id = databaseScanConfigService.insertOrUpdate(databaseScanConfigDO);
        return WebJsonBean.ok(id);
    }

    @Transactional
    @PostMapping("save_scan_config_detail")
    public WebJsonBean<?> saveScanConfigDetail(@RequestBody @Valid SaveScanConfigDetailReq req) {
        WebCheckUtils.assertNotNull(req, "缺少参数");

        List<DatabaseScanConfigDetailDO> detailDOList = req.toDetailDOList();
        databaseScanConfigService.replaceScanDetails(req.getScanConfigId(), detailDOList);

        DatabaseScanConfigVO configDO = databaseScanConfigService.getConfigById(req.getScanConfigId());
        configDO.setResultMainColumns(req.getResultMainColumns());
        databaseScanConfigService.insertOrUpdate(configDO);

        return WebJsonBean.ok();
    }

    @PostMapping("try_scan")
    public List<DatabaseScanResultDO> tryScanGroup(@RequestBody @Valid TryScanReq req) {
        WebCheckUtils.assertNotNull(req, "缺少参数");

        DatabaseScanConfigVO configDO = databaseScanConfigService.getConfigById(req.getScanConfigId());
        List<DatabaseScanConfigDetailDO> detailDOList = req.toDetailDOList();

        // 将lastkey设置到DO里
        configDO.setLastKey(req.getLastKey());
        // 将resultMainColumns设置到DO里
        configDO.setResultMainColumns(req.getResultMainColumns());

        return databaseScanService.tryScanGroup(configDO, detailDOList);
    }

    @ResponseBody @GetMapping("get_scan_result_page")
    public WebJsonBean getScanResultPage(int page, int pageSize, long databaseScanConfigId) {
        PageData<DatabaseScanResultDO> pageData = databaseScanConfigService.getScanResultPage(databaseScanConfigId, page, pageSize);
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }
}
