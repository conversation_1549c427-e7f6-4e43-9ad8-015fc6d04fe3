package com.pugwoo.branch.database.web.dto;

import com.pugwoo.branch.database.entity.DatabaseScanConfigDetailDO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class TryScanReq {

    /**
     * 从指定的key开始扫描（不含该key）
     */
    private String lastKey;

    @NotNull(message = "缺少scanConfigId")
    private Long scanConfigId;

    private String resultMainColumns;

    @Valid
    private ScanConfigDetailDTO scanConfigDetail;

    public List<DatabaseScanConfigDetailDO> toDetailDOList() {
        return scanConfigDetail.toDetailDOList(scanConfigId);
    }

}
