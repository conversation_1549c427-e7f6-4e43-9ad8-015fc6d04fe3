package com.pugwoo.branch.database.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum DatabaseScanTextTypeEnum {

    MATCH("MATCH", "匹配"),

    FILTER("FILTER", "过滤")

    ;

    final private String code;
    final private String name;

    DatabaseScanTextTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DatabaseScanTextTypeEnum getByCode(String code) {
        for (DatabaseScanTextTypeEnum e : DatabaseScanTextTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        DatabaseScanTextTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}