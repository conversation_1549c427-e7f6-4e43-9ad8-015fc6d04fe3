package com.pugwoo.branch.database.enums;

import lombok.Getter;

/**
 * <AUTHOR> <br>
 * 2022/08/20 <br>
 * 数据库类型
 */
@Getter
public enum DatabaseTypeEnum {

    MYSQL("MySQL", 3306),

    CLICKHOUSE("Click<PERSON>ouse", 8123),

    TIDB("TiDB", 4000)

    ;
    
    /** 数据库名称 */
    private final String code;
    /**默认端口*/
    private final int defaultPort;
    
    DatabaseTypeEnum(String code, int defaultPort) {
        this.code = code;
        this.defaultPort = defaultPort;
    }
    
    /**
     * 获取数据库类型枚举
     * @param code code
     * @return 枚举 如果没有匹配的返回null
     */
    public static DatabaseTypeEnum getByCode(String code) {
        for (DatabaseTypeEnum databaseType : DatabaseTypeEnum.values()) {
            if (databaseType.code.equalsIgnoreCase(code)) {
                return databaseType;
            }
        }
        return null;
    }
}
