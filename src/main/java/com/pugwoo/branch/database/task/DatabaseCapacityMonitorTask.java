package com.pugwoo.branch.database.task;

import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.branch.database.entity.DatabaseCapacityLogDO;
import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.enums.DatabaseTypeEnum;
import com.pugwoo.branch.database.model.TableSizeInfoDTO;
import com.pugwoo.branch.database.service.DatabaseService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 负责数据库的容量监控
 */
@Component
@Slf4j
public class DatabaseCapacityMonitorTask {

    @Autowired
    private DatabaseService databaseService;
    @Autowired
    private AdminNotifyService notifyService;
    @Autowired
    private DBHelper dbHelper;

    @Synchronized(throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 9 * * *")
    public void check() {
        List<DatabaseDO> capacityMonitorDatabase = databaseService.getCapacityMonitorDatabase();
        Exception ex = null;
        for (DatabaseDO databaseDO : capacityMonitorDatabase) {
            try {
                monitor(databaseDO);
            } catch (Exception e) {
                log.error("database capacity monitor error", e);
                if (ex == null) {
                    ex = e;
                }
            }
        }
        if (ex != null) {
            throw new RuntimeException(ex); // 系统问题，交给定时任务异常处理器去告警
        }
    }

    private void monitor(DatabaseDO databaseDO) {
        if (!Objects.equals(databaseDO.getType(), DatabaseTypeEnum.MYSQL.getCode())) {
            log.error("only support mysql now, databaseId:{}", databaseDO.getId());
            return;
        }

        Integer capacityGb = databaseDO.getCapacityGb();
        if (capacityGb == null || capacityGb <= 0) {
            log.error("databaseId:{} capacityGb:{} is invalid", databaseDO.getId(), capacityGb);
            return;
        }

        List<TableSizeInfoDTO> allTableFileSize = databaseService.getDatabaseOperateService(databaseDO).getAllTableFileSize(databaseDO);
        long bytes = NumberUtils.sum(allTableFileSize, TableSizeInfoDTO::getDiskSizeByte).longValue();
        double gb = ((double) bytes) / 1024 / 1024 / 1024;

        // 1. 记录capacity
        DatabaseCapacityLogDO capacityLogDO = new DatabaseCapacityLogDO();
        capacityLogDO.setDatabaseId(databaseDO.getId());
        capacityLogDO.setDay(LocalDate.now());
        capacityLogDO.setUsedGb(gb);
        dbHelper.insert(capacityLogDO);

        // 2. 超过90%时告警
        if (gb >= capacityGb * 0.9) {
            String content = "数据库" + databaseDO.getName() + " " + databaseDO.getHost() + ":" + databaseDO.getPort()
                    + " 剩余容量低于10%, 当前容量：" + String.format("%.3f", gb) + "GB，容量上限：" + capacityGb + "GB";
            notifyService.sendEmail(databaseDO.getMonitorSendMail(), "数据库容量监控", content);
            return;
        }

        // 3. 预计<=6天满时告警
        LocalDate three3Before = LocalDate.now().minusDays(3);
        DatabaseCapacityLogDO three3BeforeCapacityLogDO = dbHelper.getOne(DatabaseCapacityLogDO.class,
                "where database_id=? and day=?", databaseDO.getId(), three3Before);
        if (three3BeforeCapacityLogDO != null && three3BeforeCapacityLogDO.getUsedGb() != null) { // 不为空才可以预估
            double diff = gb - three3BeforeCapacityLogDO.getUsedGb();
            if (diff <= 0) {
                return;
            }
            if (capacityGb <= gb + diff * 2) { // 2个3天的diff就是6天
                String content = "数据库" + databaseDO.getName() + " " + databaseDO.getHost() + ":" + databaseDO.getPort()
                        + " 预计6天内用满，当前容量：" + String.format("%.3f", gb) + "GB，容量上限：" + capacityGb + "GB"
                        + ", 最近6天预计将使用" + (String.format("%.3f", diff * 2) + "GB");
                notifyService.sendEmail(databaseDO.getMonitorSendMail(), "数据库容量监控", content);
            }
        }
    }

    /**
     * 每天凌晨3点执行ANALYZE TABLE任务
     */
    @Synchronized(throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 3 * * *")
    public void analyzeTableTask() {
        List<DatabaseDO> capacityMonitorDatabase = databaseService.getCapacityMonitorDatabase();
        int totalTables = 0;
        int successTables = 0;
        int failedTables = 0;

        // 过滤掉非mysql数据库
        capacityMonitorDatabase = ListUtils.filter(capacityMonitorDatabase, o ->
                Objects.equals(o.getType(), DatabaseTypeEnum.MYSQL.getCode()));

        log.info("开始执行ANALYZE TABLE任务，共{}个mysql数据库实例", capacityMonitorDatabase.size());

        for (DatabaseDO databaseDO : capacityMonitorDatabase) {
            try {
                log.info("开始处理数据库实例：{} ({}:{})", databaseDO.getName(), databaseDO.getHost(), databaseDO.getPort());

                // 获取所有数据库名称
                List<String> databaseNames = databaseService.getDatabaseOperateService(databaseDO).listDatabaseNames(databaseDO);

                for (String databaseName : databaseNames) {
                    // 跳过系统数据库
                    if (isSystemDatabase(databaseName)) {
                        continue;
                    }

                    try {
                        // 获取该数据库下的所有表名
                        List<Map<String, Object>> tables = databaseService.executeQuery(databaseDO, databaseName, "SHOW TABLES");

                        for (Map<String, Object> tableRow : tables) {
                            String tableName = (String) tableRow.values().iterator().next();
                            totalTables++;

                            try {
                                // 执行ANALYZE TABLE
                                List<Map<String, Object>> result = databaseService.executeQuery(databaseDO, databaseName,
                                    "ANALYZE TABLE `" + tableName + "`");

                                // 检查执行结果
                                boolean isSuccess = result.stream().anyMatch(row ->
                                    "status".equalsIgnoreCase(String.valueOf(row.get("Msg_type"))) &&
                                    "OK".equalsIgnoreCase(String.valueOf(row.get("Msg_text"))));

                                if (isSuccess) {
                                    successTables++;
                                    log.debug("ANALYZE TABLE成功：{}.{}", databaseName, tableName);
                                } else {
                                    failedTables++;
                                    log.warn("ANALYZE TABLE失败：{}.{}, 结果：{}", databaseName, tableName, result);
                                }

                            } catch (Exception e) {
                                failedTables++;
                                log.error("执行ANALYZE TABLE失败：{}.{}", databaseName, tableName, e);
                            }
                        }

                        log.info("完成数据库{}的ANALYZE TABLE，表数量：{}", databaseName, tables.size());

                    } catch (Exception e) {
                        log.error("处理数据库{}时发生错误", databaseName, e);
                    }
                }

                log.info("完成数据库实例：{}", databaseDO.getName());

            } catch (Exception e) {
                log.error("处理数据库实例{}时发生错误", databaseDO.getName(), e);
            }
        }

        log.info("ANALYZE TABLE任务执行完成，总表数：{}，成功：{}，失败：{}", totalTables, successTables, failedTables);
    }

    /**
     * 判断是否为系统数据库
     */
    private boolean isSystemDatabase(String databaseName) {
        return "information_schema".equalsIgnoreCase(databaseName) ||
               "performance_schema".equalsIgnoreCase(databaseName) ||
               "mysql".equalsIgnoreCase(databaseName) ||
               "sys".equalsIgnoreCase(databaseName);
    }

}
