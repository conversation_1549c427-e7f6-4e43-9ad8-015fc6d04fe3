package com.pugwoo.branch.database.task;

import com.pugwoo.branch.database.entity.DatabaseExecuteConfigDO;
import com.pugwoo.branch.database.enums.Constants;
import com.pugwoo.branch.database.enums.DatabaseExecuteStatusEnum;
import com.pugwoo.branch.database.service.DatabaseExecuteConfigService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据库执行任务定时调度器
 * 负责检查和执行到达指定时间的任务
 */
@Component
@Slf4j
public class DatabaseExecuteScheduleTask {

    @Autowired
    private DBHelper dbHelper;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private DatabaseExecuteConfigService databaseExecuteConfigService;

    /**
     * 每分钟检查一次是否有需要执行的定时任务
     */
    @Synchronized(throwExceptionIfNotGetLock = false)
    @Scheduled(fixedDelay = 60000) // 每分钟执行一次
    public void checkScheduledTasks() {
        try {
            // 查询所有设置了定时执行时间且时间已到的任务
            List<DatabaseExecuteConfigDO> scheduledTasks = dbHelper.getAll(DatabaseExecuteConfigDO.class,
                "where schedule_at is not null and schedule_at<=? and status=?",
                 LocalDateTime.now(), DatabaseExecuteStatusEnum.WAIT.getCode());
            
            if (!scheduledTasks.isEmpty()) {
                log.info("Found {} scheduled tasks to execute", scheduledTasks.size());
            }
            
            for (DatabaseExecuteConfigDO task : scheduledTasks) {
                try {
                    log.info("Starting scheduled execution for task: {} (id: {})", task.getName(), task.getId());
                    // 启动执行任务
                    redisHelper.send(Constants.DATABASE_EXECUTE_CONFIG_TASK_MQ, task.getId().toString());
                } catch (Exception e) {
                    log.error("Failed to start scheduled task: {} (id: {})", task.getName(), task.getId(), e);
                }
            }
        } catch (Exception e) {
            log.error("Error in checkScheduledTasks", e);
        }
    }
}
