package com.pugwoo.branch.database.vo;

import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.enums.EnvEnum;
import lombok.Data;

/**
 * <AUTHOR> <br>
 * 2022/08/20 <br>
 *
 */
@Data
public class DatabaseVO extends DatabaseDO {

    /**json需要*/
    @SuppressWarnings("unused")
    public String getEnvLabel() {
        EnvEnum e = EnvEnum.getByCode(getEnv());
        return e == null ? getEnv() : e.getName();
    }

}
