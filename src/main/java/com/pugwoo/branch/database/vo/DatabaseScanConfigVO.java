package com.pugwoo.branch.database.vo;

import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.entity.DatabaseScanConfigDO;
import com.pugwoo.branch.database.entity.DatabaseScanConfigDetailDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

@Data
public class DatabaseScanConfigVO extends DatabaseScanConfigDO {

    @RelatedColumn(localColumn = "id", remoteColumn = "scan_config_id")
    private List<DatabaseScanConfigDetailDO> details;

    @RelatedColumn(localColumn = "database_id", remoteColumn = "id")
    private DatabaseDO databaseDO;

}
