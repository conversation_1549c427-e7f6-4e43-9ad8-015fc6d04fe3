package com.pugwoo.branch.database.vo;

import com.pugwoo.branch.database.entity.DatabaseStdWideTableColumnDO;
import com.pugwoo.branch.database.entity.DatabaseStdWideTableColumnEnumDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 标准宽表列VO，扩展了DO类，增加了枚举值关联
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class DatabaseStdWideTableColumnVO extends DatabaseStdWideTableColumnDO {

    /** 列的枚举值列表 */
    @RelatedColumn(localColumn = "id", remoteColumn = "column_id")
    private List<DatabaseStdWideTableColumnEnumDO> enumValues;

}
