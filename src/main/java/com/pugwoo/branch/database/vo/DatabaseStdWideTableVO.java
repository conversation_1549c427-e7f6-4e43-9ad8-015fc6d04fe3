package com.pugwoo.branch.database.vo;

import com.pugwoo.branch.database.entity.DatabaseDO;
import com.pugwoo.branch.database.entity.DatabaseStdWideTableDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Setter;

public class DatabaseStdWideTableVO extends DatabaseStdWideTableDO {

    @Setter // 这里不用getter是不想暴露这个do，因为它有密码
    @RelatedColumn(localColumn = "database_id", remoteColumn = "id")
    private DatabaseDO databaseDO;

    /**vm用到*/
    public String getDatabase() {
        return databaseDO == null ? "" : databaseDO.getName();
    }

}
