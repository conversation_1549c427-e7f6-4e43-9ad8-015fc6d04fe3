package com.pugwoo.branch.database.model;

import lombok.Data;

/**
 * sql线程的锁信息
 */
@Data
public class ProcessListLockDTO {

    /** 锁范围的类型，例如 METADATA_LOCK */
    private String scopeType;

    /** 锁类型 */
    private String lockType;

    /**是否是写锁，要么是读锁，要么是写锁*/
    private Boolean isWriteLock;

    /** 锁时长 */
    private String lockDuration;

    /** 锁状态 */
    private String lockStatus;

    /** 锁住的东西的类型 */
    private String objectType;

    /** 锁住的数据库 */
    private String database;

    /** 锁住的表 */
    private String table;

    /** 锁住的列 */
    private String column;

    /**锁住的行*/
    private String row;

    /**锁相关索引*/
    private String lockedIndex;

    /**如果是等待锁，这里是等待的时长，不一定有值*/
    private Integer waitingSeconds;

    /**当前事务已经锁住的行数*/
    private Integer rowsLocked;

    /**等待哪个pid*/
    private Long waitProcessId;

}
