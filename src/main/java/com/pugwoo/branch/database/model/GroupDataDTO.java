package com.pugwoo.branch.database.model;

import lombok.Data;

import java.util.List;

@Data
public class GroupDataDTO {

    private String columnName;

    /**列注释，从create table里拿，如果有的话*/
    private String columnComment;

    private Integer distinctValueCount;

    /**按count排序的top，个数视参数来*/
    private List<ValueAndCount> topValueAndCount;

    private List<ValueAndCount> bottomValueAndCount;

    private Object maxValue;

    private Object minValue;

    @Data
    public static class ValueAndCount {
        private Object value;
        private Object count;
    }

}
