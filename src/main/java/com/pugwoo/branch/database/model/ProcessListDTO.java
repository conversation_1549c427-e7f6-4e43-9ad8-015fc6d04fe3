package com.pugwoo.branch.database.model;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.util.List;

/**
 * 数据库sql进程信息
 */
@Data
public class ProcessListDTO {

    /**process id*/
    private Long id;

    /**执行该sql的用户*/
    private String user;

    /**客户端的ip，可能有端口*/
    private String clientIp;

    /**操作的db*/
    private String db;

    /**和mysql的command对应*/
    private String command;

    /**执行的sql*/
    private String sql;

    /**已经执行的时间*/
    private Integer time;

    /**状态*/
    private String state;

    /**持有的锁*/
    private List<ProcessListLockDTO> locks;

    /**持有的读锁的个数*/
    private Integer holdReadLocks;

    /**持有的写锁的个数*/
    private Integer holdWriteLocks;

    /**等待的读锁的个数*/
    private Integer waitReadLocks;

    /**等待的写锁的个数*/
    private Integer waitWriteLocks;

    /**持有的行锁的个数*/
    private Integer holdRowLocks;

    /**等待的行锁的个数*/
    private Integer waitRowLocks;

    /**当前事务中undo记录数*/
    private Integer undoEntries;

    public boolean isSleepAndNoLocksNoUndo() {
        if (!"Sleep".equalsIgnoreCase(command)) {
            return false;
        }
        if (ListUtils.isNotEmpty(locks)) {
            return false;
        }
        if (holdRowLocks != null && holdRowLocks > 0) {
            return false;
        }
        if (waitRowLocks != null && waitRowLocks > 0) {
            return false;
        }
        if (undoEntries != null && undoEntries > 0) {
            return false;
        }

        return true;
    }

}
