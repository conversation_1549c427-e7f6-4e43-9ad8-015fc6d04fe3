package com.pugwoo.branch.database.utils;

import java.util.ArrayList;
import java.util.List;

public class BranchSQLUtils {

    /**
     * 将分号隔开的多条sql拆分成list sql
     */
    public static List<String> splitSql(String sqls) {
        List<String> statements = new ArrayList<>();
        StringBuilder statementBuilder = new StringBuilder();
        boolean insideQuotes = false;
        char quoteChar = '\0'; // '\0'表示未设置引号字符

        for (char c : sqls.toCharArray()) {
            if (c == '\'' || c == '"') {
                if (!insideQuotes) {
                    insideQuotes = true;
                    quoteChar = c;
                } else if (c == quoteChar) {
                    insideQuotes = false;
                    quoteChar = '\0';
                }
            }

            if (c == ';' && !insideQuotes) {
                String sql = statementBuilder.toString().trim();
                if (!sql.isEmpty()) {
                    statements.add(sql);
                }
                statementBuilder.setLength(0);
            } else {
                statementBuilder.append(c);
            }
        }

        if (!statementBuilder.isEmpty()) {
            String sql = statementBuilder.toString().trim();
            if (!sql.isEmpty()) {
                statements.add(sql);
            }
        }

        return statements;
    }

}
