package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("database_execute_result")
public class DatabaseExecuteResultDO extends AdminCoreDO {

    /** 执行任务名称，冗余字段<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 关联database_execute_task的id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 执行结果状态，FAILED失败，SUCCESS成功<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 实际执行的SQL<br/>Column: [sql] */
    @Column(value = "sql")
    private String sql;

    /** 实际修改的行数<br/>Column: [affected_rows] */
    @Column(value = "affected_rows")
    private Integer affectedRows;

    /** 当前执行的主键值，取起始值<br/>Column: [current_key_value] */
    @Column(value = "current_key_value")
    private String currentKeyValue;

    /** 错误信息<br/>Column: [error_message] */
    @Column(value = "error_message")
    private String errorMessage;

    /** 执行时间（毫秒）<br/>Column: [execution_time] */
    @Column(value = "execution_time")
    private Integer executionTime;

}