package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 数据库运行参数
 */
@Data
@ToString
@Table("database_variables")
public class DatabaseVariablesDO extends AdminCoreDO {

    /** 变量名<br/>Column: [variable] */
    @Column(value = "variable")
    private String variable;

    /** 变量名称，一般是中文或可读名称<br/>Column: [variable_name] */
    @Column(value = "variable_name")
    private String variableName;

    /** 数据库类型，例如MYSQL<br/>Column: [database_type] */
    @Column(value = "database_type")
    private String databaseType;

    /** 对应的查询sql<br/>Column: [sql] */
    @Column(value = "sql")
    private String sql;

    /** 该参数的默认值<br/>Column: [default_value] */
    @Column(value = "default_value")
    private String defaultValue;

    /** 说明<br/>Column: [note] */
    @Column(value = "note")
    private String note;

    /** 转换数值的方式<br/>Column: [cast_value_method] */
    @Column(value = "cast_value_method")
    private String castValueMethod;

}