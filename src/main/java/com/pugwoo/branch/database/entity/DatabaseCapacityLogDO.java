package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 数据库容量记录
 */
@Data
@ToString
@Table("database_capacity_log")
public class DatabaseCapacityLogDO extends AdminCoreDO {

    /** 数据库实例id<br/>Column: [database_id] */
    @Column(value = "database_id")
    private Long databaseId;

    /** 日期<br/>Column: [day] */
    @Column(value = "day")
    private LocalDate day;

    /** 当前用量大小，单位GB<br/>Column: [used_gb] */
    @Column(value = "used_gb")
    private Double usedGb;

}