package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * <AUTHOR> <br>
 * 2022/08/20 <br>
 *
 */
@Data
@Table("database")
public class DatabaseDO extends AdminBaseDO {
    
    /** 连接名称<br>Column: [name] */
    @Column(value = "name")
    private String name;
    
    /** 环境名称: develop/test/product<br>Column: [env] */
    @Column(value = "env")
    private String env;
    
    /** 数据库类型: mysql/clickhouse等，@see DatabaseTypeEnum <br>Column: [type] */
    @Column(value = "type")
    private String type;
    
    /** 数据库主机<br>Column: [host] */
    @Column(value = "host")
    private String host;
    
    /** 数据库端口<br>Column: [port] */
    @Column(value = "port")
    private Integer port;
    
    /** 用户名<br>Column: [username] */
    @Column(value = "username")
    private String username;
    
    /** 用户密码<br>Column: [password] */
    @Column(value = "password")
    private String password;

    /** 是否开启监控(内置)<br/>Column: [enable_monitor] */
    @Column(value = "enable_monitor")
    private Boolean enableMonitor;

    /** 是否开启容量监控<br/>Column: [enable_capacity_monitor] */
    @Column(value = "enable_capacity_monitor")
    private Boolean enableCapacityMonitor;

    /** 数据库容量上限，GB<br/>Column: [capacity_gb] */
    @Column(value = "capacity_gb")
    private Integer capacityGb;

    /** 分号隔开，错误时发送邮件；复用后也支持钉钉<br/>Column: [monitor_send_mail] */
    @Column(value = "monitor_send_mail")
    private String monitorSendMail;
}
