package com.pugwoo.branch.database.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("database_scan_result")
public class DatabaseScanResultDO extends AdminCoreDO {

    /** database_scan_config的id<br/>Column: [scan_config_id] */
    @Column(value = "scan_config_id")
    private Long scanConfigId;

    /** 命中的表的id<br/>Column: [target_table_id] */
    @Column(value = "target_table_id")
    private String targetTableId;

    /** 命中的表的主要字段<br/>Column: [target_main_column] */
    @Column(value = "target_main_column")
    private String targetMainColumn;

}
