package com.pugwoo.branch.code_scan.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.util.Date;

@Data
@Table("git_code_check_rule_detail")
public class GitCodeCheckRuleDetailDO {
    /** 主键<br>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 规则主键<br>Column: [rule_id] */
    @Column(value = "rule_id")
    private Long ruleId;

    /** 类型<br>Column: [type] */
    @Column(value = "type")
    private String type;

    /** 内容<br>Column: [contentRegex] */
    @Column(value = "content_regex")
    private String contentRegex;

    /** 类型<br>Column: [level] */
    @Column(value = "level")
    private String level;

    /** 创建时间<br>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /**创建人id*/
    @Column(value = "create_user_id",
            insertValueScript = "com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor.getPossibleUserId()")
    private Long createUserId;

    /**修改人id*/
    @Column(value = "update_user_id",
            updateValueScript = "com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor.getPossibleUserId()",
            deleteValueScript = "com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor.getPossibleUserId()")
    private Long updateUserId;

    /** 更新时间<br>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;
}