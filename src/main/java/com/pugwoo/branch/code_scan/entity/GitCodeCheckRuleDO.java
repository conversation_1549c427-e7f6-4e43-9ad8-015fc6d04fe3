package com.pugwoo.branch.code_scan.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.util.Date;

@Data
@Table("git_code_check_rule")
public class GitCodeCheckRuleDO {
    /** 主键id<br>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 规则名称<br>Column: [name] */
    @Column(value = "name")
    private String name;

    @Column(value = "create_user_id",
            insertValueScript = "com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor.getPossibleUserId()")
    private Long createUserId;

    /** 创建时间<br>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /**修改人id*/
    @Column(value = "update_user_id",
            updateValueScript = "com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor.getPossibleUserId()",
            deleteValueScript = "com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor.getPossibleUserId()")
    private Long updateUserId;

    /** 更新时间<br>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;
}