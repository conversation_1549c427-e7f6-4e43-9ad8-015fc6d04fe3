package com.pugwoo.branch.code_scan.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.util.Date;

@Data
@Table("git_code_check_result")
public class GitCodeCheckResultDO {

    /** 主键<br>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**对应的发布id*/
    @Column("ingress_id")
    private Long ingressId;

    /**错误级别*/
    @Column("error_level")
    private String errorLevel;

    @Column(value = "filename")
    private String filename;

    /** 扫描结果<br>Column: [check_msg] */
    @Column(value = "check_msg")
    private String checkMsg;

    /** 创建时间<br>Column: [create_time] */
    @Column(value = "create_time",setTimeWhenInsert = true)
    private Date createTime;
}