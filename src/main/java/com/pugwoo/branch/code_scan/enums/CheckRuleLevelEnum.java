package com.pugwoo.branch.code_scan.enums;

public enum CheckRuleLevelEnum {
    INFO("INFO","正常"),
    WARNING("WARNING", "警告"),
    ERROR("ERROR", "严重"),
    ;

    private String code;

    private String name;

    CheckRuleLevelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static CheckRuleLevelEnum getByCode(String code) {
        for (CheckRuleLevelEnum e : CheckRuleLevelEnum.values()) {
            if (code == e.getCode() || code != null && code.equals(e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        CheckRuleLevelEnum e = getByCode(code);
        if (e != null) {
            return e.getName();
        }
        return "";
    }
}
