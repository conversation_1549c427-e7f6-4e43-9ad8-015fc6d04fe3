package com.pugwoo.branch.code_scan.enums;

public enum  CheckRuleTypeEnum {
    MUST_NOT("MUST_NOT", "不能出现"),
    ;

    private String code;

    private String name;

    CheckRuleTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static CheckRuleTypeEnum getByCode(String code) {
        for (CheckRuleTypeEnum e : CheckRuleTypeEnum.values()) {
            if (code == e.getCode() || code != null && code.equals(e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        CheckRuleTypeEnum e = getByCode(code);
        if (e != null) {
            return e.getName();
        }
        return "";
    }
}