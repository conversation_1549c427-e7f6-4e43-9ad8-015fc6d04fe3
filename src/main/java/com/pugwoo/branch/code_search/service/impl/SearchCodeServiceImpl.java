package com.pugwoo.branch.code_search.service.impl;

import com.pugwoo.branch.code_search.model.SearchCodeDO;
import com.pugwoo.branch.code_search.service.ISearchCodeService;
import com.pugwoo.branch.code_search.utils.FileUtils;
import com.pugwoo.branch.git.config.Constants;
import com.pugwoo.branch.git.entity.GitRepositoryDO;
import com.pugwoo.branch.git.service.IGitCloneService;
import com.pugwoo.branch.git.service.IGitRepositoryService;
import com.pugwoo.wooutils.redis.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;

@Slf4j
@Service
public class SearchCodeServiceImpl implements ISearchCodeService {

    public static final String TOTAL = "total";

    public static final String COMPLETE = "complete";

    public static final String LAST_NUMBER = "last_number";

    public static final String LIST = "list";

    @Autowired
    RedisHelper redisHelper;

    @Autowired
    private IGitRepositoryService repositoryService;
    @Autowired
    private IGitCloneService gitCloneService;

    @Override
    public String startSearch(String searchReg) throws IOException {
        //查询出所有仓库
        List<GitRepositoryDO> gitRepositoryDOS = repositoryService.getAll();
        if (gitRepositoryDOS == null || gitRepositoryDOS.size() == 0) {
            return "";
        }
        String uuid = UUID.randomUUID().toString().replace("-", "");
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        //比例插入
        redisHelper.setObject(getRedisKey(uuid, TOTAL), 3600, gitRepositoryDOS.size());
        redisHelper.setObject(getRedisKey(uuid, COMPLETE), 3600, 0);

        for (GitRepositoryDO gitRepositoryDO : gitRepositoryDOS) {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        searchRepository(gitRepositoryDO, searchReg, uuid);
                    } catch (Exception e) {
                        log.error("search repo {} fail", gitRepositoryDO.getName(), e);
                    }
                }
            });

        }
        executorService.shutdown();
        return uuid;
    }

    /**
     * @Description: 异步查询用户列表
     * @date: 2019-10-17
     * @Return
     */
    private void searchRepository(GitRepositoryDO gitRepositoryDO, String searchReg, String uuid) throws Exception {
        String path = gitCloneService.copyRepo(gitRepositoryDO);

        try {
            Pattern pattern = Pattern.compile(searchReg, Pattern.CASE_INSENSITIVE); // 默认忽略大小写
            List<File> files = FileUtils.listFiles(new File(path));
            for (File file : files) {
                BufferedReader br = new BufferedReader(new FileReader(file));
                String line = null;
                int lineCount = 1;
                while ((line = br.readLine()) != null) {
                    if (pattern.matcher(line).find()) {
                        Integer lastId = redisHelper.getObject(getRedisKey(uuid, LAST_NUMBER), Integer.class);
                        if (lastId == null) {
                            lastId = 0;
                        }
                        SearchCodeDO searchCodeDO = new SearchCodeDO();
                        searchCodeDO.setBranch("master");
                        searchCodeDO.setSerialNumber(lastId + 1);
                        searchCodeDO.setRepository(gitRepositoryDO.getName());
                        String projectPath = file.getPath().replace('\\', '/')
                                .replace(Constants.LOCAL_COPY_PATH,"");
                        searchCodeDO.setCodeLine(projectPath.substring(32) + "[" + lineCount + "]");
                        searchCodeDO.setContent(line.trim());
                        List<SearchCodeDO> old = redisHelper.getObject(getRedisKey(uuid, LIST), List.class, SearchCodeDO.class);
                        if (old == null) {
                            old = new ArrayList<>();
                        }
                        old.add(searchCodeDO);
                        redisHelper.setObject(getRedisKey(uuid, LAST_NUMBER), 3600, lastId + 1);
                        redisHelper.setObject(getRedisKey(uuid, LIST), 3600, old);
                    }
                    lineCount++;
                }
                br.close();
            }

            redisHelper.execute(jedis -> jedis.incr(getRedisKey(uuid, COMPLETE)));
        } finally {
            org.apache.commons.io.FileUtils.forceDelete(new File(path));
        }
    }

    private String getRedisKey(String uuid, String key) {
        return Constants.REDIS_NAME_SPACE + Constants.SEARCH + ":" + uuid + ":" + key;
    }

    /**
     * 获得查询数据
     *
     * @param uuid
     */
    @Override
    public List<SearchCodeDO> getList(String uuid) {
        List list = redisHelper.getObject(getRedisKey(uuid, "list"), List.class, SearchCodeDO.class);
        if (list == null) {
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * @param uuid
     * @Description: 获取进度
     * @date: 2019-10-17
     */
    @Override
    public Integer getProgress(String uuid) {
        Integer total = redisHelper.getObject(getRedisKey(uuid, TOTAL), Integer.class);
        Integer complete = redisHelper.getObject(getRedisKey(uuid, COMPLETE), Integer.class);
        if (total == null || total == 0 || complete == null) {
            return 0;
        }
        Integer rise = new BigDecimal(((float) complete / total) * 100).setScale(2, BigDecimal.ROUND_HALF_UP).intValue();
        return rise;
    }


}
