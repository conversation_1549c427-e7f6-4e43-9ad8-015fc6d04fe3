package com.pugwoo.branch.code_search.utils;

import com.pugwoo.wooutils.collect.ListUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class FileUtils {

    /**
     *
     * @Description: 遍历文件夹下的所有文件
     * @date:  2019-10-17
     * @Return
     *
     */
    public static List<File> listFiles(File file) {
        // 忽略.git目录和.gitignore文件
        String fileName = file.getName();
        if(fileName.equals(".git") || fileName.equals(".gitignore")) {
            return new ArrayList<>();
        }
        // 忽略一些常见的二进制文件
        if(fileName.toLowerCase().endsWith(".jpg") || fileName.toLowerCase().endsWith(".png")) {
            return new ArrayList<>();
        }

        if (file.isFile()) {
            return ListUtils.newArrayList(file);
        }

        List<File> result = new ArrayList<>();
        File[] files = file.listFiles();
        if(files == null) {
            return result;
        }
        for (File f : files) {
            result.addAll(listFiles(f));
        }
        return result;
    }
}
